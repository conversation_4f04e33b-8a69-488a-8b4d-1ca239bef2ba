"""
Task completion tool for Make It Heavy system
"""

from typing import List
from .base_tool import BaseTool, ToolParameter

class MarkTaskCompleteTool(BaseTool):
    """Signal that a task has been completed with summary and results"""
    
    def get_parameters(self) -> List[ToolParameter]:
        return [
            Too<PERSON><PERSON>arameter("task_summary", "string", "Brief summary of what was accomplished"),
            ToolParameter("completion_message", "string", "Detailed completion message with results")
        ]
    
    async def execute(self, task_summary: str, completion_message: str) -> str:
        """Mark task as complete and return formatted completion message"""
        self.validate_parameters(task_summary=task_summary, completion_message=completion_message)
        
        return f"""**Task Completed Successfully**

**Summary:** {task_summary}

**Results:**
{completion_message}

**Status:** ✅ COMPLETE"""
