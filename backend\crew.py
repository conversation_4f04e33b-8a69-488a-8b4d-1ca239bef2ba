from crewai import Agent, Task, Crew, Process
from crewai_tools import Ser<PERSON>DevTool, WebsiteSearchTool
import asyncio
import os
from datetime import datetime
from openrouter_llm import create_openrouter_llm

class ChatCrew:
    def __init__(self):
        """Initialize the CrewAI chat crew with specialized agents"""
        # Initialize OpenRouter LLM
        self.llm = create_openrouter_llm()
        self.setup_agents()
        self.setup_crew()
    
    def setup_agents(self):
        """Create specialized AI agents for different tasks"""
        
        # Initialize tools
        search_tool = SerperDevTool() if os.getenv("SERPER_API_KEY") else None
        web_tool = WebsiteSearchTool()
        
        # Chat Assistant Agent - Primary conversational agent
        self.chat_agent = Agent(
            role="AI Chat Assistant",
            goal="Provide helpful, accurate, and engaging responses to user queries",
            backstory="""You are AG3NT X, an advanced AI assistant with expertise across
            multiple domains. You excel at understanding context, providing detailed explanations,
            and helping users solve problems. You communicate in a friendly, professional manner
            and always strive to be helpful and accurate.""",
            verbose=True,
            allow_delegation=True,
            llm=self.llm,
            tools=[tool for tool in [search_tool, web_tool] if tool is not None]
        )
        
        # Research Agent - For complex queries requiring research
        self.research_agent = Agent(
            role="Research Specialist",
            goal="Conduct thorough research and provide comprehensive information",
            backstory="""You are a research specialist who excels at finding accurate,
            up-to-date information from reliable sources. You analyze complex topics and
            present findings in a clear, organized manner.""",
            verbose=True,
            allow_delegation=False,
            llm=self.llm,
            tools=[tool for tool in [search_tool, web_tool] if tool is not None]
        )
        
        # Problem Solver Agent - For technical and analytical tasks
        self.problem_solver = Agent(
            role="Problem Solving Expert",
            goal="Analyze problems and provide step-by-step solutions",
            backstory="""You are an expert problem solver with strong analytical skills.
            You break down complex problems into manageable steps and provide clear,
            actionable solutions. You excel at technical explanations and troubleshooting.""",
            verbose=True,
            allow_delegation=False,
            llm=self.llm
        )
    
    def setup_crew(self):
        """Initialize the crew with agents"""
        self.crew = Crew(
            agents=[self.chat_agent, self.research_agent, self.problem_solver],
            process=Process.sequential,
            verbose=True,
            memory=True
        )
    
    async def process_message(self, message: str) -> str:
        """Process a user message and return a response"""
        try:
            # Create a task for the message
            task = Task(
                description=f"""
                User message: "{message}"
                
                Provide a helpful, accurate, and engaging response to this user message. 
                If the message requires research or complex problem-solving, delegate to 
                the appropriate specialist agents. Always maintain a friendly and professional tone.
                """,
                expected_output="A comprehensive and helpful response to the user's message",
                agent=self.chat_agent
            )
            
            # Create a temporary crew for this specific task
            temp_crew = Crew(
                agents=[self.chat_agent, self.research_agent, self.problem_solver],
                tasks=[task],
                process=Process.sequential,
                verbose=False  # Reduce verbosity for cleaner output
            )
            
            # Execute the task
            result = temp_crew.kickoff()
            
            # Extract the response text
            if hasattr(result, 'raw'):
                return result.raw
            elif isinstance(result, str):
                return result
            else:
                return str(result)
                
        except Exception as e:
            return f"I apologize, but I encountered an error while processing your message: {str(e)}"
    
    async def stream_message(self, message: str):
        """Stream the response for real-time chat experience"""
        try:
            # For now, we'll simulate streaming by processing the message
            # and yielding chunks of the response
            response = await self.process_message(message)
            
            # Split response into chunks for streaming effect
            words = response.split()
            chunk_size = 3  # Words per chunk
            
            for i in range(0, len(words), chunk_size):
                chunk = " ".join(words[i:i + chunk_size])
                if i + chunk_size < len(words):
                    chunk += " "
                yield chunk
                await asyncio.sleep(0.1)  # Small delay for streaming effect
                
        except Exception as e:
            yield f"Error: {str(e)}"
    
    def get_agent_info(self):
        """Return information about available agents"""
        return {
            "chat_agent": {
                "role": self.chat_agent.role,
                "goal": self.chat_agent.goal
            },
            "research_agent": {
                "role": self.research_agent.role,
                "goal": self.research_agent.goal
            },
            "problem_solver": {
                "role": self.problem_solver.role,
                "goal": self.problem_solver.goal
            }
        }
