"""
Web search tool for Make It Heavy system
"""

import httpx
from typing import List
from .base_tool import BaseTool, ToolParameter

class SearchWebTool(BaseTool):
    """Search the web using DuckDuckGo for comprehensive information gathering"""
    
    def get_parameters(self) -> List[ToolParameter]:
        return [
            ToolParameter("query", "string", "Search query to execute"),
            ToolParameter("max_results", "integer", "Maximum number of results to return", False, 5)
        ]
    
    async def execute(self, query: str, max_results: int = 5) -> str:
        """Execute web search and return formatted results"""
        self.validate_parameters(query=query, max_results=max_results)
        
        try:
            # In a real implementation, you would integrate with DuckDuckGo API
            # For now, we'll simulate search results
            
            search_results = [
                {
                    "title": f"Search Result {i+1} for '{query}'",
                    "url": f"https://example{i+1}.com",
                    "snippet": f"This is a comprehensive result about {query} with detailed information and insights."
                }
                for i in range(min(max_results, 5))
            ]
            
            formatted_results = []
            for i, result in enumerate(search_results, 1):
                formatted_results.append(
                    f"{i}. **{result['title']}**\n"
                    f"   URL: {result['url']}\n"
                    f"   Summary: {result['snippet']}\n"
                )
            
            return f"**Web Search Results for '{query}':**\n\n" + "\n".join(formatted_results)
            
        except Exception as e:
            return f"Error performing web search: {str(e)}"
