from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
import asyncio
import json
import os
from dotenv import load_dotenv
from crew import ChatCrew

# Load environment variables
load_dotenv()

app = FastAPI(title="AG3NT X Backend", version="1.0.0")

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class ChatMessage(BaseModel):
    message: str
    conversation_id: str = "default"

class ChatResponse(BaseModel):
    response: str
    agent: str
    timestamp: str

# Initialize the CrewAI chat crew
chat_crew = ChatCrew()

@app.get("/")
async def root():
    return {"message": "AG3NT X Backend is running"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "AG3NT X Backend"}

@app.post("/api/chat")
async def chat_endpoint(chat_message: ChatMessage):
    """
    Handle chat messages and return CrewAI responses
    """
    try:
        # Process the message with CrewAI
        response = await chat_crew.process_message(chat_message.message)
        
        return {
            "response": response,
            "agent": "AG3NT X",
            "timestamp": "2024-01-01T00:00:00Z",
            "conversation_id": chat_message.conversation_id
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing message: {str(e)}")

@app.post("/api/chat/stream")
async def chat_stream_endpoint(chat_message: ChatMessage):
    """
    Handle chat messages with streaming responses
    """
    async def generate_response():
        try:
            # Process the message with CrewAI and stream the response
            async for chunk in chat_crew.stream_message(chat_message.message):
                yield f"data: {json.dumps({'content': chunk, 'done': False})}\n\n"
            
            # Send completion signal
            yield f"data: {json.dumps({'content': '', 'done': True})}\n\n"
            
        except Exception as e:
            error_data = {"error": str(e), "done": True}
            yield f"data: {json.dumps(error_data)}\n\n"
    
    return StreamingResponse(
        generate_response(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream",
        }
    )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
