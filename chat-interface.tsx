"use client"

import type React from "react"
import { useState, useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Send, Co<PERSON>, ThumbsUp, ThumbsDown, Paperclip, X, Zap, Brain, Eye, EyeOff, Clock, CheckCircle, XCircle } from "lucide-react"

interface Message {
  id: string
  role: "user" | "agent"
  content: string
  timestamp: Date
  files?: File[]
}

interface UploadedFile {
  file: File
  id: string
}

export default function ChatInterface() {
  const [input, setInput] = useState("")
  const [messages, setMessages] = useState<Message[]>([])
  const [isTyping, setIsTyping] = useState(false)
  const [isChatMode, setIsChatMode] = useState(false)
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([])
  const [mode, setMode] = useState<"single" | "heavy">("single")
  const [heavyProgress, setHeavyProgress] = useState<string>("")
  const [agentSteps, setAgentSteps] = useState<Array<{
    step: string
    agent: string
    content: string
    timestamp: Date
    status: 'running' | 'completed' | 'failed'
  }>>([])
  const [showVerbose, setShowVerbose] = useState(true)
  const scrollAreaRef = useRef<HTMLDivElement>(null)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const scrollToBottom = () => {
    if (scrollAreaRef.current) {
      const scrollContainer = scrollAreaRef.current.querySelector("[data-radix-scroll-area-viewport]")
      if (scrollContainer) {
        scrollContainer.scrollTop = scrollContainer.scrollHeight
      }
    }
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto"
      textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 120)}px`
    }
  }, [input])

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (files) {
      const newFiles = Array.from(files).map((file) => ({
        file,
        id: Math.random().toString(36).substr(2, 9),
      }))
      setUploadedFiles((prev) => [...prev, ...newFiles])
    }
    // Reset the input so the same file can be selected again
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  const removeFile = (fileId: string) => {
    setUploadedFiles((prev) => prev.filter((f) => f.id !== fileId))
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes"
    const k = 1024
    const sizes = ["Bytes", "KB", "MB", "GB"]
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return Number.parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
  }

  const addAgentStep = (step: string, agent: string, content: string, status: 'running' | 'completed' | 'failed' = 'running') => {
    setAgentSteps(prev => [...prev, {
      step,
      agent,
      content,
      timestamp: new Date(),
      status
    }])
  }

  const updateLastAgentStep = (status: 'completed' | 'failed', content?: string) => {
    setAgentSteps(prev => {
      const updated = [...prev]
      if (updated.length > 0) {
        updated[updated.length - 1].status = status
        if (content) {
          updated[updated.length - 1].content = content
        }
      }
      return updated
    })
  }

  const sendMessageToAI = async (userMessage: string) => {
    setIsTyping(true)
    setHeavyProgress("")
    setAgentSteps([])

    try {
      console.log(`🚀 Starting ${mode} mode analysis for: "${userMessage}"`)

      if (mode === "heavy") {
        setHeavyProgress("🧠 Initializing Make It Heavy multi-agent system...")
        addAgentStep("Initialization", "System", "Starting Make It Heavy analysis", "running")

        // Simulate the heavy mode process with verbose logging
        setHeavyProgress("🎯 Generating specialized research questions...")
        addAgentStep("Question Generation", "AI Generator", "Creating 4 specialized research questions", "running")

        await new Promise(resolve => setTimeout(resolve, 1000))
        updateLastAgentStep("completed", "Generated 4 specialized questions for parallel analysis")

        // Simulate parallel agent execution
        const agents = [
          { name: "Researcher", task: "Gathering facts and background information" },
          { name: "Analyst", task: "Providing deep insights and implications" },
          { name: "Contextualizer", task: "Exploring alternatives and broader context" },
          { name: "Verifier", task: "Cross-checking and validating information" }
        ]

        setHeavyProgress("🔀 Deploying 4 specialized agents in parallel...")

        for (const agent of agents) {
          addAgentStep("Agent Deployment", agent.name, `${agent.task}`, "running")
          await new Promise(resolve => setTimeout(resolve, 500))
        }

        // Simulate agent work
        for (let i = 0; i < agents.length; i++) {
          setHeavyProgress(`⚡ Agent ${agents[i].name} analyzing...`)
          await new Promise(resolve => setTimeout(resolve, 1500))
          updateLastAgentStep("completed", `Completed ${agents[i].task.toLowerCase()}`)

          if (i < agents.length - 1) {
            addAgentStep("Agent Work", agents[i + 1].name, `Working on: ${agents[i + 1].task}`, "running")
          }
        }

        setHeavyProgress("🔄 Synthesizing comprehensive analysis...")
        addAgentStep("Synthesis", "Synthesis Agent", "Combining all agent perspectives", "running")
        await new Promise(resolve => setTimeout(resolve, 1000))
      }

      // Make the actual API call
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: userMessage,
          conversation_id: 'default',
          mode: mode,
        }),
      })

      console.log(`📡 API Response Status: ${response.status}`)

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      console.log('📦 Received response data:', data)

      if (mode === "heavy") {
        updateLastAgentStep("completed", "Successfully synthesized comprehensive analysis")
        setHeavyProgress("✅ Make It Heavy analysis complete!")

        // Show additional details if available
        if (data.agent_results) {
          console.log('🔍 Agent Results:', data.agent_results)
          data.agent_results.forEach((result: any, index: number) => {
            addAgentStep("Agent Result", result.agent_type, `${result.question}\n\nResult: ${result.result.substring(0, 100)}...`, "completed")
          })
        }

        if (data.generated_questions) {
          console.log('❓ Generated Questions:', data.generated_questions)
        }
      }

      const agentMessage: Message = {
        id: Date.now().toString() + "-agent",
        role: "agent",
        content: data.response || "I apologize, but I couldn't process your request at the moment.",
        timestamp: new Date(),
      }

      setMessages((prev) => [...prev, agentMessage])

    } catch (error) {
      console.error('❌ Error sending message to AI:', error)

      if (mode === "heavy") {
        updateLastAgentStep("failed", `Error: ${error}`)
        setHeavyProgress("❌ Make It Heavy analysis failed")
      }

      const errorMessage: Message = {
        id: Date.now().toString() + "-agent",
        role: "agent",
        content: "I apologize, but I'm having trouble connecting to my AI systems right now. Please try again in a moment.",
        timestamp: new Date(),
      }

      setMessages((prev) => [...prev, errorMessage])
    } finally {
      setIsTyping(false)
      setTimeout(() => {
        setHeavyProgress("")
        setAgentSteps([])
      }, 3000) // Keep verbose info for 3 seconds after completion
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!input.trim() && uploadedFiles.length === 0) return

    const userMessage: Message = {
      id: Date.now().toString(),
      role: "user",
      content: input.trim(),
      timestamp: new Date(),
      files: uploadedFiles.map((f) => f.file),
    }

    setMessages((prev) => [...prev, userMessage])
    setInput("")
    setUploadedFiles([])

    if (!isChatMode) {
      setIsChatMode(true)
    }

    // Send message to AI backend
    await sendMessageToAI(input.trim())
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSubmit(e)
    }
  }

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
  }

  const ChatBubble = ({ message }: { message: Message }) => {
    const isUser = message.role === "user"

    return (
      <div className={`flex gap-3 mb-6 ${isUser ? "justify-end" : "justify-start"}`}>
        {!isUser && (
          <div className={`w-8 h-8 rounded-full ${mode === 'heavy' ? 'bg-gradient-to-br from-purple-500 to-pink-600' : 'bg-gradient-to-br from-blue-500 to-purple-600'} flex items-center justify-center flex-shrink-0 mt-1`}>
            <span className="text-xs font-semibold text-white">{mode === 'heavy' ? 'H' : 'AI'}</span>
          </div>
        )}

        <div className={`max-w-[70%] ${isUser ? "order-first" : ""}`}>
          <div className="flex items-center gap-2 mb-1">
            <span className="text-xs font-medium text-neutral-500">{isUser ? "You" : (mode === 'heavy' ? "Make It Heavy" : "AG3NT X")}</span>
            <span className="text-xs text-neutral-500">{formatTime(message.timestamp)}</span>
          </div>

          <div
            className={`
            px-4 py-3 rounded-2xl text-sm leading-relaxed whitespace-pre-wrap
            ${isUser ? "bg-blue-500 text-white ml-auto rounded-br-md" : "bg-neutral-800 text-neutral-100 rounded-bl-md"}
          `}
          >
            {message.content}
          </div>

          {/* File attachments */}
          {message.files && message.files.length > 0 && (
            <div className="mt-2 space-y-2">
              {message.files.map((file, index) => (
                <div
                  key={index}
                  className={`
                  flex items-center gap-2 px-3 py-2 rounded-lg text-xs
                  ${isUser ? "bg-blue-700/50 text-blue-100" : "bg-neutral-700/50 text-neutral-300"}
                `}
                >
                  <Paperclip className="h-3 w-3" />
                  <span className="truncate">{file.name}</span>
                  <span className="text-neutral-500">({formatFileSize(file.size)})</span>
                </div>
              ))}
            </div>
          )}

          {!isUser && (
            <div className="flex items-center gap-1 mt-2">
              <Button variant="ghost" size="sm" className="h-7 w-7 p-0 hover:bg-neutral-800">
                <Copy className="h-3 w-3 text-gray-500" />
              </Button>
              <Button variant="ghost" size="sm" className="h-7 w-7 p-0 hover:bg-neutral-800">
                <ThumbsUp className="h-3 w-3 text-gray-500" />
              </Button>
              <Button variant="ghost" size="sm" className="h-7 w-7 p-0 hover:bg-neutral-800">
                <ThumbsDown className="h-3 w-3 text-gray-500" />
              </Button>
            </div>
          )}
        </div>

        {isUser && (
          <div className="w-8 h-8 rounded-full bg-gradient-to-br from-green-500 to-teal-600 flex items-center justify-center flex-shrink-0 mt-1">
            <span className="text-xs font-semibold text-white">U</span>
          </div>
        )}
      </div>
    )
  }

  const TypingIndicator = () => (
    <div className="flex gap-3 mb-6">
      <div className={`w-8 h-8 rounded-full ${mode === 'heavy' ? 'bg-gradient-to-br from-purple-500 to-pink-600' : 'bg-gradient-to-br from-blue-500 to-purple-600'} flex items-center justify-center flex-shrink-0 mt-1`}>
        <span className="text-xs font-semibold text-white">{mode === 'heavy' ? 'H' : 'AI'}</span>
      </div>
      <div className="bg-gray-800 px-4 py-3 rounded-2xl rounded-bl-md">
        {heavyProgress ? (
          <div className="text-sm text-blue-400">
            {heavyProgress}
          </div>
        ) : (
          <div className="flex gap-1">
            <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: "0ms" }}></div>
            <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: "150ms" }}></div>
            <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: "300ms" }}></div>
          </div>
        )}
      </div>
    </div>
  )

  const ModeSelector = () => (
    <div className="flex items-center gap-2 mb-4">
      <span className="text-sm text-neutral-400">Mode:</span>
      <div className="flex bg-neutral-900 border border-neutral-800 rounded-lg p-1">
        <button
          onClick={() => setMode("single")}
          className={`flex items-center gap-2 px-3 py-1.5 rounded-md text-sm transition-all ${
            mode === "single"
              ? "bg-blue-500/10 text-blue-400 border border-blue-500/20"
              : "text-neutral-400 hover:text-white hover:bg-neutral-800"
          }`}
        >
          <Zap className="h-4 w-4" />
          Single Agent
        </button>
        <button
          onClick={() => setMode("heavy")}
          className={`flex items-center gap-2 px-3 py-1.5 rounded-md text-sm transition-all ${
            mode === "heavy"
              ? "bg-purple-500/10 text-purple-400 border border-purple-500/20"
              : "text-neutral-400 hover:text-white hover:bg-neutral-800"
          }`}
        >
          <Brain className="h-4 w-4" />
          Make It Heavy
        </button>
      </div>
      {(agentSteps.length > 0 || heavyProgress) && (
        <button
          onClick={() => setShowVerbose(!showVerbose)}
          className="flex items-center gap-1 px-2 py-1 rounded-md text-xs bg-neutral-800 hover:bg-neutral-700 text-neutral-400 hover:text-white transition-all"
        >
          {showVerbose ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
          {showVerbose ? "Hide" : "Show"} Details
        </button>
      )}
    </div>
  )

  const VerbosePanel = () => {
    if (!showVerbose || (agentSteps.length === 0 && !heavyProgress)) return null

    return (
      <div className="mb-4 bg-neutral-900 border border-neutral-800 rounded-lg p-4">
        <div className="flex items-center gap-2 mb-3">
          <Brain className="h-4 w-4 text-purple-400" />
          <span className="text-sm font-medium text-white">Make It Heavy - Agent Communications</span>
        </div>

        {heavyProgress && (
          <div className="mb-3 p-3 bg-purple-500/10 border border-purple-500/20 rounded-lg">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-purple-400 animate-spin" />
              <span className="text-sm text-purple-300">{heavyProgress}</span>
            </div>
          </div>
        )}

        <div className="space-y-2 max-h-64 overflow-y-auto">
          {agentSteps.map((step, index) => (
            <div key={index} className="flex items-start gap-3 p-2 bg-neutral-800 rounded-lg">
              <div className="flex-shrink-0 mt-1">
                {step.status === 'running' && <Clock className="h-4 w-4 text-yellow-400 animate-spin" />}
                {step.status === 'completed' && <CheckCircle className="h-4 w-4 text-green-400" />}
                {step.status === 'failed' && <XCircle className="h-4 w-4 text-red-400" />}
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <span className="text-xs font-medium text-white">{step.agent}</span>
                  <span className="text-xs text-neutral-500">•</span>
                  <span className="text-xs text-neutral-400">{step.step}</span>
                  <span className="text-xs text-neutral-500">•</span>
                  <span className="text-xs text-neutral-500">{step.timestamp.toLocaleTimeString()}</span>
                </div>
                <p className="text-xs text-neutral-300 leading-relaxed">{step.content}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  const FileUploadArea = () => (
    <>
      {uploadedFiles.length > 0 && (
        <div className="mb-3 space-y-2">
          {uploadedFiles.map((uploadedFile) => (
            <div
              key={uploadedFile.id}
              className="flex items-center gap-2 px-3 py-2 bg-neutral-800/50 border border-neutral-700 rounded-lg"
            >
              <Paperclip className="h-4 w-4 text-gray-400" />
              <span className="text-sm text-gray-300 truncate flex-1">{uploadedFile.file.name}</span>
              <span className="text-xs text-gray-500">({formatFileSize(uploadedFile.file.size)})</span>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => removeFile(uploadedFile.id)}
                className="h-6 w-6 p-0 hover:bg-gray-700"
              >
                <X className="h-3 w-3 text-gray-400" />
              </Button>
            </div>
          ))}
        </div>
      )}
    </>
  )

  if (!isChatMode) {
    return (
      <div className="flex flex-col h-full">
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center max-w-2xl mx-auto px-8">
            <h1 className="text-5xl font-medium text-white mb-8 leading-tight">How can I assist you?</h1>

            <div className="mb-8">
              <ModeSelector />
              {mode === "heavy" && (
                <div className="text-sm text-purple-400 bg-purple-500/10 border border-purple-500/20 rounded-lg p-3 mt-3">
                  <div className="flex items-center gap-2 mb-1">
                    <Brain className="h-4 w-4" />
                    <span className="font-medium">Make It Heavy Mode</span>
                  </div>
                  <p className="text-purple-300">
                    Multi-agent analysis with 4 specialized AI agents working in parallel for comprehensive insights.
                  </p>
                </div>
              )}
              <VerbosePanel />
            </div>

            <form onSubmit={handleSubmit} className="relative">
              <FileUploadArea />
              <div className="textarea-container relative">
                <Textarea
                  ref={textareaRef}
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder="Start a new conversation..."
                  className="textarea-with-button w-full min-h-[48px] max-h-[120px] px-4 py-3 pr-20 bg-neutral-900 border-neutral-800 text-white placeholder:text-neutral-500 focus:border-blue-500/40 focus:ring-0 focus:outline-none focus-visible:ring-0 focus-visible:ring-offset-0 rounded-lg resize-none dark-scrollbar"
                  rows={1}
                />
                <input
                  ref={fileInputRef}
                  type="file"
                  multiple
                  onChange={handleFileUpload}
                  className="hidden"
                  accept="*/*"
                />
                <Button
                  type="button"
                  size="sm"
                  onClick={() => fileInputRef.current?.click()}
                  className="absolute right-12 bottom-2 h-8 w-8 p-0 bg-neutral-700 hover:bg-neutral-600 border-0 z-10"
                  title="Upload files"
                >
                  <Paperclip className="h-4 w-4" />
                </Button>
                <Button
                  type="submit"
                  size="sm"
                  className="absolute right-2 bottom-2 h-8 w-8 p-0 bg-blue-600 hover:bg-blue-700 border-0 z-10"
                  disabled={!input.trim() && uploadedFiles.length === 0}
                >
                  <Send className="h-4 w-4" />
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col h-full">
      {/* Chat Header */}
      <div className="h-16 px-4 border-b border-neutral-800 flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className={`w-8 h-8 rounded-full ${mode === 'heavy' ? 'bg-gradient-to-br from-purple-500 to-pink-600' : 'bg-gradient-to-br from-blue-500 to-purple-600'} flex items-center justify-center`}>
            <span className="text-xs font-semibold text-white">{mode === 'heavy' ? 'H' : 'AI'}</span>
          </div>
          <div>
            <h2 className="font-medium text-white">{mode === 'heavy' ? 'Make It Heavy' : 'AG3NT X'}</h2>
            <p className="text-xs text-neutral-500">{mode === 'heavy' ? 'Multi-Agent System' : 'Single Agent'}</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <ModeSelector />
        </div>
      </div>

      {/* Messages Area */}
      {/* Verbose Panel in Chat Mode */}
      <VerbosePanel />

      <ScrollArea ref={scrollAreaRef} className="flex-1 p-4">
        <div className="space-y-0">
          {messages.map((message) => (
            <ChatBubble key={message.id} message={message} />
          ))}
          {isTyping && <TypingIndicator />}
        </div>
      </ScrollArea>

      {/* Input Area */}
      <div className="p-4 border-t border-neutral-800">
        <form onSubmit={handleSubmit} className="relative">
          <FileUploadArea />
          <div className="textarea-container relative">
            <Textarea
              ref={textareaRef}
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Type your message..."
              className="textarea-with-button w-full min-h-[48px] max-h-[120px] px-4 py-3 pr-20 bg-neutral-900 border-neutral-800 text-white placeholder:text-neutral-500 focus:border-blue-500/40 focus:ring-0 focus:outline-none focus-visible:ring-0 focus-visible:ring-offset-0 rounded-lg resize-none dark-scrollbar"
              rows={1}
              disabled={isTyping}
            />
            <input
              ref={fileInputRef}
              type="file"
              multiple
              onChange={handleFileUpload}
              className="hidden"
              accept="*/*"
            />
            <Button
              type="button"
              size="sm"
              onClick={() => fileInputRef.current?.click()}
              className="absolute right-12 bottom-2 h-8 w-8 p-0 bg-neutral-700 hover:bg-neutral-600 border-0 z-10"
              title="Upload files"
              disabled={isTyping}
            >
              <Paperclip className="h-4 w-4" />
            </Button>
            <Button
              type="submit"
              size="sm"
              className="absolute right-2 bottom-2 h-8 w-8 p-0 bg-blue-600 hover:bg-blue-700 border-0 z-10"
              disabled={(!input.trim() && uploadedFiles.length === 0) || isTyping}
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}
