"use client"

import type React from "react"
import { useState, useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Send, Copy, ThumbsUp, Thum<PERSON>Down, Paperclip, X, Zap, Brain } from "lucide-react"

interface Message {
  id: string
  role: "user" | "agent"
  content: string
  timestamp: Date
  files?: File[]
}

interface UploadedFile {
  file: File
  id: string
}

export default function ChatInterface() {
  const [input, setInput] = useState("")
  const [messages, setMessages] = useState<Message[]>([])
  const [isTyping, setIsTyping] = useState(false)
  const [isChatMode, setIsChatMode] = useState(false)
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([])
  const [mode, setMode] = useState<"single" | "heavy">("single")
  const [heavyProgress, setHeavyProgress] = useState<string>("")
  const scrollAreaRef = useRef<HTMLDivElement>(null)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const scrollToBottom = () => {
    if (scrollAreaRef.current) {
      const scrollContainer = scrollAreaRef.current.querySelector("[data-radix-scroll-area-viewport]")
      if (scrollContainer) {
        scrollContainer.scrollTop = scrollContainer.scrollHeight
      }
    }
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto"
      textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 120)}px`
    }
  }, [input])

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (files) {
      const newFiles = Array.from(files).map((file) => ({
        file,
        id: Math.random().toString(36).substr(2, 9),
      }))
      setUploadedFiles((prev) => [...prev, ...newFiles])
    }
    // Reset the input so the same file can be selected again
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  const removeFile = (fileId: string) => {
    setUploadedFiles((prev) => prev.filter((f) => f.id !== fileId))
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes"
    const k = 1024
    const sizes = ["Bytes", "KB", "MB", "GB"]
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return Number.parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
  }

  const sendMessageToAI = async (userMessage: string) => {
    setIsTyping(true)
    setHeavyProgress("")

    try {
      if (mode === "heavy") {
        // Use streaming endpoint for heavy mode
        console.log('Sending heavy mode request to /api/chat/heavy')
        const response = await fetch('/api/chat/heavy', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            message: userMessage,
            conversation_id: 'default',
            mode: 'heavy',
          }),
        })

        console.log('Heavy mode response status:', response.status)
        if (!response.ok) {
          console.error('Heavy mode response error:', response.status, response.statusText)
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const reader = response.body?.getReader()
        const decoder = new TextDecoder()
        let finalResponse = ""

        if (reader) {
          while (true) {
            const { done, value } = await reader.read()
            if (done) break

            const chunk = decoder.decode(value)
            const lines = chunk.split('\n')

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                try {
                  const data = JSON.parse(line.slice(6))

                  if (data.type === "progress") {
                    setHeavyProgress(data.message)
                  } else if (data.type === "complete") {
                    finalResponse = data.response
                    setHeavyProgress("")
                  } else if (data.type === "error") {
                    throw new Error(data.error)
                  }
                } catch (e) {
                  console.error('Error parsing SSE data:', e)
                }
              }
            }
          }
        }

        const agentMessage: Message = {
          id: Date.now().toString() + "-agent",
          role: "agent",
          content: finalResponse || "I apologize, but I couldn't process your request at the moment.",
          timestamp: new Date(),
        }

        setMessages((prev) => [...prev, agentMessage])
      } else {
        // Single agent mode
        const response = await fetch('/api/chat', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            message: userMessage,
            conversation_id: 'default',
            mode: 'single',
          }),
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const data = await response.json()

        const agentMessage: Message = {
          id: Date.now().toString() + "-agent",
          role: "agent",
          content: data.response || "I apologize, but I couldn't process your request at the moment.",
          timestamp: new Date(),
        }

        setMessages((prev) => [...prev, agentMessage])
      }
    } catch (error) {
      console.error('Error sending message to AI:', error)

      // If heavy mode fails, try fallback to regular endpoint
      if (mode === "heavy") {
        console.log('Heavy mode failed, trying fallback to regular endpoint')
        try {
          const fallbackResponse = await fetch('/api/chat', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              message: userMessage,
              conversation_id: 'default',
              mode: 'heavy',
            }),
          })

          if (fallbackResponse.ok) {
            const data = await fallbackResponse.json()
            const agentMessage: Message = {
              id: Date.now().toString() + "-agent",
              role: "agent",
              content: data.response || "I apologize, but I couldn't process your request at the moment.",
              timestamp: new Date(),
            }
            setMessages((prev) => [...prev, agentMessage])
            return
          }
        } catch (fallbackError) {
          console.error('Fallback also failed:', fallbackError)
        }
      }

      const errorMessage: Message = {
        id: Date.now().toString() + "-agent",
        role: "agent",
        content: "I apologize, but I'm having trouble connecting to my AI systems right now. Please try again in a moment.",
        timestamp: new Date(),
      }

      setMessages((prev) => [...prev, errorMessage])
    } finally {
      setIsTyping(false)
      setHeavyProgress("")
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!input.trim() && uploadedFiles.length === 0) return

    const userMessage: Message = {
      id: Date.now().toString(),
      role: "user",
      content: input.trim(),
      timestamp: new Date(),
      files: uploadedFiles.map((f) => f.file),
    }

    setMessages((prev) => [...prev, userMessage])
    setInput("")
    setUploadedFiles([])

    if (!isChatMode) {
      setIsChatMode(true)
    }

    // Send message to AI backend
    await sendMessageToAI(input.trim())
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSubmit(e)
    }
  }

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
  }

  const ChatBubble = ({ message }: { message: Message }) => {
    const isUser = message.role === "user"

    return (
      <div className={`flex gap-3 mb-6 ${isUser ? "justify-end" : "justify-start"}`}>
        {!isUser && (
          <div className={`w-8 h-8 rounded-full ${mode === 'heavy' ? 'bg-gradient-to-br from-purple-500 to-pink-600' : 'bg-gradient-to-br from-blue-500 to-purple-600'} flex items-center justify-center flex-shrink-0 mt-1`}>
            <span className="text-xs font-semibold text-white">{mode === 'heavy' ? 'H' : 'AI'}</span>
          </div>
        )}

        <div className={`max-w-[70%] ${isUser ? "order-first" : ""}`}>
          <div className="flex items-center gap-2 mb-1">
            <span className="text-xs font-medium text-neutral-500">{isUser ? "You" : (mode === 'heavy' ? "Make It Heavy" : "AG3NT X")}</span>
            <span className="text-xs text-neutral-500">{formatTime(message.timestamp)}</span>
          </div>

          <div
            className={`
            px-4 py-3 rounded-2xl text-sm leading-relaxed whitespace-pre-wrap
            ${isUser ? "bg-blue-500 text-white ml-auto rounded-br-md" : "bg-neutral-800 text-neutral-100 rounded-bl-md"}
          `}
          >
            {message.content}
          </div>

          {/* File attachments */}
          {message.files && message.files.length > 0 && (
            <div className="mt-2 space-y-2">
              {message.files.map((file, index) => (
                <div
                  key={index}
                  className={`
                  flex items-center gap-2 px-3 py-2 rounded-lg text-xs
                  ${isUser ? "bg-blue-700/50 text-blue-100" : "bg-neutral-700/50 text-neutral-300"}
                `}
                >
                  <Paperclip className="h-3 w-3" />
                  <span className="truncate">{file.name}</span>
                  <span className="text-neutral-500">({formatFileSize(file.size)})</span>
                </div>
              ))}
            </div>
          )}

          {!isUser && (
            <div className="flex items-center gap-1 mt-2">
              <Button variant="ghost" size="sm" className="h-7 w-7 p-0 hover:bg-neutral-800">
                <Copy className="h-3 w-3 text-gray-500" />
              </Button>
              <Button variant="ghost" size="sm" className="h-7 w-7 p-0 hover:bg-neutral-800">
                <ThumbsUp className="h-3 w-3 text-gray-500" />
              </Button>
              <Button variant="ghost" size="sm" className="h-7 w-7 p-0 hover:bg-neutral-800">
                <ThumbsDown className="h-3 w-3 text-gray-500" />
              </Button>
            </div>
          )}
        </div>

        {isUser && (
          <div className="w-8 h-8 rounded-full bg-gradient-to-br from-green-500 to-teal-600 flex items-center justify-center flex-shrink-0 mt-1">
            <span className="text-xs font-semibold text-white">U</span>
          </div>
        )}
      </div>
    )
  }

  const TypingIndicator = () => (
    <div className="flex gap-3 mb-6">
      <div className={`w-8 h-8 rounded-full ${mode === 'heavy' ? 'bg-gradient-to-br from-purple-500 to-pink-600' : 'bg-gradient-to-br from-blue-500 to-purple-600'} flex items-center justify-center flex-shrink-0 mt-1`}>
        <span className="text-xs font-semibold text-white">{mode === 'heavy' ? 'H' : 'AI'}</span>
      </div>
      <div className="bg-gray-800 px-4 py-3 rounded-2xl rounded-bl-md">
        {heavyProgress ? (
          <div className="text-sm text-blue-400">
            {heavyProgress}
          </div>
        ) : (
          <div className="flex gap-1">
            <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: "0ms" }}></div>
            <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: "150ms" }}></div>
            <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: "300ms" }}></div>
          </div>
        )}
      </div>
    </div>
  )

  const ModeSelector = () => (
    <div className="flex items-center gap-2 mb-4">
      <span className="text-sm text-neutral-400">Mode:</span>
      <div className="flex bg-neutral-900 border border-neutral-800 rounded-lg p-1">
        <button
          onClick={() => setMode("single")}
          className={`flex items-center gap-2 px-3 py-1.5 rounded-md text-sm transition-all ${
            mode === "single"
              ? "bg-blue-500/10 text-blue-400 border border-blue-500/20"
              : "text-neutral-400 hover:text-white hover:bg-neutral-800"
          }`}
        >
          <Zap className="h-4 w-4" />
          Single Agent
        </button>
        <button
          onClick={() => setMode("heavy")}
          className={`flex items-center gap-2 px-3 py-1.5 rounded-md text-sm transition-all ${
            mode === "heavy"
              ? "bg-purple-500/10 text-purple-400 border border-purple-500/20"
              : "text-neutral-400 hover:text-white hover:bg-neutral-800"
          }`}
        >
          <Brain className="h-4 w-4" />
          Make It Heavy
        </button>
      </div>
    </div>
  )

  const FileUploadArea = () => (
    <>
      {uploadedFiles.length > 0 && (
        <div className="mb-3 space-y-2">
          {uploadedFiles.map((uploadedFile) => (
            <div
              key={uploadedFile.id}
              className="flex items-center gap-2 px-3 py-2 bg-neutral-800/50 border border-neutral-700 rounded-lg"
            >
              <Paperclip className="h-4 w-4 text-gray-400" />
              <span className="text-sm text-gray-300 truncate flex-1">{uploadedFile.file.name}</span>
              <span className="text-xs text-gray-500">({formatFileSize(uploadedFile.file.size)})</span>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => removeFile(uploadedFile.id)}
                className="h-6 w-6 p-0 hover:bg-gray-700"
              >
                <X className="h-3 w-3 text-gray-400" />
              </Button>
            </div>
          ))}
        </div>
      )}
    </>
  )

  if (!isChatMode) {
    return (
      <div className="flex flex-col h-full">
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center max-w-2xl mx-auto px-8">
            <h1 className="text-5xl font-medium text-white mb-8 leading-tight">How can I assist you?</h1>

            <div className="mb-8">
              <ModeSelector />
              {mode === "heavy" && (
                <div className="text-sm text-purple-400 bg-purple-500/10 border border-purple-500/20 rounded-lg p-3 mt-3">
                  <div className="flex items-center gap-2 mb-1">
                    <Brain className="h-4 w-4" />
                    <span className="font-medium">Make It Heavy Mode</span>
                  </div>
                  <p className="text-purple-300">
                    Multi-agent analysis with 4 specialized AI agents working in parallel for comprehensive insights.
                  </p>
                </div>
              )}
            </div>

            <form onSubmit={handleSubmit} className="relative">
              <FileUploadArea />
              <div className="textarea-container relative">
                <Textarea
                  ref={textareaRef}
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder="Start a new conversation..."
                  className="textarea-with-button w-full min-h-[48px] max-h-[120px] px-4 py-3 pr-20 bg-neutral-900 border-neutral-800 text-white placeholder:text-neutral-500 focus:border-blue-500/40 focus:ring-0 focus:outline-none focus-visible:ring-0 focus-visible:ring-offset-0 rounded-lg resize-none dark-scrollbar"
                  rows={1}
                />
                <input
                  ref={fileInputRef}
                  type="file"
                  multiple
                  onChange={handleFileUpload}
                  className="hidden"
                  accept="*/*"
                />
                <Button
                  type="button"
                  size="sm"
                  onClick={() => fileInputRef.current?.click()}
                  className="absolute right-12 bottom-2 h-8 w-8 p-0 bg-neutral-700 hover:bg-neutral-600 border-0 z-10"
                  title="Upload files"
                >
                  <Paperclip className="h-4 w-4" />
                </Button>
                <Button
                  type="submit"
                  size="sm"
                  className="absolute right-2 bottom-2 h-8 w-8 p-0 bg-blue-600 hover:bg-blue-700 border-0 z-10"
                  disabled={!input.trim() && uploadedFiles.length === 0}
                >
                  <Send className="h-4 w-4" />
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col h-full">
      {/* Chat Header */}
      <div className="h-16 px-4 border-b border-neutral-800 flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className={`w-8 h-8 rounded-full ${mode === 'heavy' ? 'bg-gradient-to-br from-purple-500 to-pink-600' : 'bg-gradient-to-br from-blue-500 to-purple-600'} flex items-center justify-center`}>
            <span className="text-xs font-semibold text-white">{mode === 'heavy' ? 'H' : 'AI'}</span>
          </div>
          <div>
            <h2 className="font-medium text-white">{mode === 'heavy' ? 'Make It Heavy' : 'AG3NT X'}</h2>
            <p className="text-xs text-neutral-500">{mode === 'heavy' ? 'Multi-Agent System' : 'Single Agent'}</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <ModeSelector />
        </div>
      </div>

      {/* Messages Area */}
      <ScrollArea ref={scrollAreaRef} className="flex-1 p-4">
        <div className="space-y-0">
          {messages.map((message) => (
            <ChatBubble key={message.id} message={message} />
          ))}
          {isTyping && <TypingIndicator />}
        </div>
      </ScrollArea>

      {/* Input Area */}
      <div className="p-4 border-t border-neutral-800">
        <form onSubmit={handleSubmit} className="relative">
          <FileUploadArea />
          <div className="textarea-container relative">
            <Textarea
              ref={textareaRef}
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Type your message..."
              className="textarea-with-button w-full min-h-[48px] max-h-[120px] px-4 py-3 pr-20 bg-neutral-900 border-neutral-800 text-white placeholder:text-neutral-500 focus:border-blue-500/40 focus:ring-0 focus:outline-none focus-visible:ring-0 focus-visible:ring-offset-0 rounded-lg resize-none dark-scrollbar"
              rows={1}
              disabled={isTyping}
            />
            <input
              ref={fileInputRef}
              type="file"
              multiple
              onChange={handleFileUpload}
              className="hidden"
              accept="*/*"
            />
            <Button
              type="button"
              size="sm"
              onClick={() => fileInputRef.current?.click()}
              className="absolute right-12 bottom-2 h-8 w-8 p-0 bg-neutral-700 hover:bg-neutral-600 border-0 z-10"
              title="Upload files"
              disabled={isTyping}
            >
              <Paperclip className="h-4 w-4" />
            </Button>
            <Button
              type="submit"
              size="sm"
              className="absolute right-2 bottom-2 h-8 w-8 p-0 bg-blue-600 hover:bg-blue-700 border-0 z-10"
              disabled={(!input.trim() && uploadedFiles.length === 0) || isTyping}
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}
