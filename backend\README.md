# AG3NT X Backend

This is the Python backend for AG3NT X, powered by CrewAI for intelligent multi-agent conversations using OpenRouter.

## Setup

1. **Install Python dependencies:**
   ```bash
   cd backend
   pip install -r requirements.txt
   ```

2. **Set up environment variables:**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys
   ```

3. **Required API Keys:**
   - `OPENROUTER_API_KEY`: Required for CrewAI agents (provides access to multiple AI models)
   - `OPENROUTER_MODEL`: Optional, defaults to Claude 3.5 Sonnet
   - `SERPER_API_KEY`: Optional, for web search capabilities

## Running the Backend

```bash
cd backend
python main.py
```

The backend will start on `http://localhost:8000`

## API Endpoints

- `GET /`: Health check
- `POST /api/chat`: Send a message and get a response
- `POST /api/chat/stream`: Send a message and get a streaming response

## CrewAI Agents

The backend includes three specialized agents:

1. **Chat Assistant**: Primary conversational agent
2. **Research Specialist**: For complex queries requiring research
3. **Problem Solver**: For technical and analytical tasks

## OpenRouter Integration

This backend uses OpenRouter instead of direct OpenAI API calls, providing:

- **Multiple Model Access**: Choose from Claude, GPT-4, Gemini, Llama, and more
- **Cost Optimization**: Access to various pricing tiers
- **Reliability**: Fallback options if one model is unavailable
- **Flexibility**: Easy model switching via environment variables

### Available Models

Popular models you can use by setting `OPENROUTER_MODEL`:

- `anthropic/claude-3.5-sonnet` (default, best overall performance)
- `openai/gpt-4o` (OpenAI's latest)
- `google/gemini-pro` (Google's model)
- `meta-llama/llama-3.1-405b-instruct` (Open source)
- `mistralai/mistral-large` (European model)

## Development

The backend uses FastAPI for the web server and CrewAI for the AI agent orchestration. The agents work together to provide comprehensive responses to user queries, powered by OpenRouter's multi-model API.
