# AG3NT X Backend

This is the Python backend for AG3NT X, powered by CrewAI for intelligent multi-agent conversations.

## Setup

1. **Install Python dependencies:**
   ```bash
   cd backend
   pip install -r requirements.txt
   ```

2. **Set up environment variables:**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys
   ```

3. **Required API Keys:**
   - `OPENAI_API_KEY`: Required for CrewAI agents
   - `SERPER_API_KEY`: Optional, for web search capabilities

## Running the Backend

```bash
cd backend
python main.py
```

The backend will start on `http://localhost:8000`

## API Endpoints

- `GET /`: Health check
- `POST /api/chat`: Send a message and get a response
- `POST /api/chat/stream`: Send a message and get a streaming response

## CrewAI Agents

The backend includes three specialized agents:

1. **Chat Assistant**: Primary conversational agent
2. **Research Specialist**: For complex queries requiring research
3. **Problem Solver**: For technical and analytical tasks

## Development

The backend uses FastAPI for the web server and CrewAI for the AI agent orchestration. The agents work together to provide comprehensive responses to user queries.
