#!/bin/bash

# AG3NT X Development Startup Script

echo "🚀 Starting AG3NT X Development Environment..."

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is required but not installed."
    exit 1
fi

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is required but not installed."
    exit 1
fi

# Function to start backend
start_backend() {
    echo "🐍 Starting CrewAI Backend..."
    cd backend
    
    # Check if virtual environment exists
    if [ ! -d "venv" ]; then
        echo "📦 Creating Python virtual environment..."
        python3 -m venv venv
    fi
    
    # Activate virtual environment
    source venv/bin/activate
    
    # Install dependencies
    echo "📦 Installing Python dependencies..."
    pip install -r requirements.txt
    
    # Check for .env file
    if [ ! -f ".env" ]; then
        echo "⚠️  No .env file found. Please copy .env.example to .env and add your OpenRouter API key."
        cp .env.example .env
        echo "📝 Created .env file from template. Please edit it with your OpenRouter API key."
        echo "🔑 Get your OpenRouter API key from: https://openrouter.ai/keys"
    fi
    
    # Start the backend
    echo "🚀 Starting FastAPI backend on http://localhost:8000"
    python main.py &
    BACKEND_PID=$!
    cd ..
}

# Function to start frontend
start_frontend() {
    echo "⚛️  Starting Next.js Frontend..."
    
    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        echo "📦 Installing Node.js dependencies..."
        npm install
    fi
    
    # Check for .env.local file
    if [ ! -f ".env.local" ]; then
        echo "📝 Creating .env.local from template..."
        cp .env.local.example .env.local
    fi
    
    # Start the frontend
    echo "🚀 Starting Next.js frontend on http://localhost:3000"
    npm run dev &
    FRONTEND_PID=$!
}

# Start both services
start_backend
sleep 3  # Give backend time to start
start_frontend

echo ""
echo "✅ AG3NT X is now running!"
echo "🌐 Frontend: http://localhost:3000"
echo "🔧 Backend API: http://localhost:8000"
echo ""
echo "Press Ctrl+C to stop all services"

# Wait for user to stop
wait

# Cleanup
echo "🛑 Stopping services..."
kill $BACKEND_PID 2>/dev/null
kill $FRONTEND_PID 2>/dev/null
echo "✅ All services stopped"
