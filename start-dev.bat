@echo off
echo 🚀 Starting AG3NT X Development Environment...

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is required but not installed.
    pause
    exit /b 1
)

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js is required but not installed.
    pause
    exit /b 1
)

echo 🐍 Starting CrewAI Backend...
cd backend

REM Check if virtual environment exists
if not exist "venv" (
    echo 📦 Creating Python virtual environment...
    python -m venv venv
)

REM Activate virtual environment
call venv\Scripts\activate.bat

REM Install dependencies
echo 📦 Installing Python dependencies...
pip install -r requirements.txt

REM Check for .env file
if not exist ".env" (
    echo ⚠️  No .env file found. Creating from template...
    copy .env.example .env
    echo 📝 Created .env file from template. Please edit it with your API keys.
)

REM Start the backend
echo 🚀 Starting FastAPI backend on http://localhost:8000
start "AG3NT X Backend" cmd /k "python main.py"

cd ..

REM Wait a moment for backend to start
timeout /t 3 /nobreak >nul

echo ⚛️  Starting Next.js Frontend...

REM Install dependencies if needed
if not exist "node_modules" (
    echo 📦 Installing Node.js dependencies...
    npm install
)

REM Check for .env.local file
if not exist ".env.local" (
    echo 📝 Creating .env.local from template...
    copy .env.local.example .env.local
)

REM Start the frontend
echo 🚀 Starting Next.js frontend on http://localhost:3000
start "AG3NT X Frontend" cmd /k "npm run dev"

echo.
echo ✅ AG3NT X is now running!
echo 🌐 Frontend: http://localhost:3000
echo 🔧 Backend API: http://localhost:8000
echo.
echo Press any key to exit...
pause >nul
