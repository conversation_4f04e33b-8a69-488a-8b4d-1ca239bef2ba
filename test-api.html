<!DOCTYPE html>
<html>
<head>
    <title>Test Make It Heavy API</title>
</head>
<body>
    <h1>Test Make It Heavy API</h1>
    <button onclick="testSingleMode()">Test Single Mode</button>
    <button onclick="testHeavyMode()">Test Heavy Mode</button>
    <div id="results"></div>

    <script>
        async function testSingleMode() {
            const results = document.getElementById('results');
            results.innerHTML = 'Testing Single Mode...';
            
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: 'Hello test',
                        mode: 'single'
                    })
                });
                
                const data = await response.json();
                results.innerHTML = `<h3>Single Mode Result:</h3><pre>${JSON.stringify(data, null, 2)}</pre>`;
            } catch (error) {
                results.innerHTML = `<h3>Single Mode Error:</h3><pre>${error.message}</pre>`;
            }
        }

        async function testHeavyMode() {
            const results = document.getElementById('results');
            results.innerHTML = 'Testing Heavy Mode...';
            
            try {
                const response = await fetch('/api/chat/heavy', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: 'Test heavy analysis',
                        conversation_id: 'test'
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let result = '';
                
                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;
                    
                    const chunk = decoder.decode(value);
                    result += chunk;
                    results.innerHTML = `<h3>Heavy Mode Streaming:</h3><pre>${result}</pre>`;
                }
                
            } catch (error) {
                results.innerHTML = `<h3>Heavy Mode Error:</h3><pre>${error.message}</pre>`;
            }
        }
    </script>
</body>
</html>
