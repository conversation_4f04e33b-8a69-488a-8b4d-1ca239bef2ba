# AG3NT X - AI Chat Interface with CrewAI

A modern AI chat interface built with Next.js and powered by CrewAI for intelligent multi-agent conversations.

## 🚀 Features

- **Modern UI**: Clean, dark-themed interface with <PERSON><PERSON><PERSON> font
- **CrewAI Integration**: Multi-agent AI system for comprehensive responses
- **Real-time Chat**: Instant messaging with typing indicators
- **File Upload**: Support for file attachments
- **Responsive Design**: Works on desktop and mobile devices
- **Agent Collaboration**: Multiple specialized AI agents working together

## 🏗️ Architecture

- **Frontend**: Next.js 15 with TypeScript and Tailwind CSS
- **Backend**: FastAPI with CrewAI integration
- **AI Agents**: Specialized agents for chat, research, and problem-solving
- **API**: RESTful endpoints with streaming support

## 📋 Prerequisites

- **Node.js** 18+ 
- **Python** 3.8+
- **OpenAI API Key** (required for CrewAI)
- **Serper API Key** (optional, for web search)

## 🛠️ Quick Start

### Option 1: Automated Setup (Recommended)

**On Windows:**
```bash
start-dev.bat
```

**On macOS/Linux:**
```bash
chmod +x start-dev.sh
./start-dev.sh
```

### Option 2: Manual Setup

1. **Clone and install frontend dependencies:**
   ```bash
   npm install
   ```

2. **Set up the backend:**
   ```bash
   cd backend
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt
   ```

3. **Configure environment variables:**
   ```bash
   # Frontend
   cp .env.local.example .env.local
   
   # Backend
   cd backend
   cp .env.example .env
   # Edit .env with your API keys
   ```

4. **Start the services:**
   ```bash
   # Terminal 1: Start backend
   cd backend
   python main.py
   
   # Terminal 2: Start frontend
   npm run dev
   ```

## 🔑 Environment Variables

### Backend (.env)
```env
OPENAI_API_KEY=your_openai_api_key_here
SERPER_API_KEY=your_serper_api_key_here  # Optional
```

### Frontend (.env.local)
```env
BACKEND_URL=http://localhost:8000
```

## 🤖 CrewAI Agents

The system includes three specialized agents:

1. **Chat Assistant**: Primary conversational agent for general queries
2. **Research Specialist**: Conducts thorough research for complex topics
3. **Problem Solver**: Handles technical and analytical problems

## 📡 API Endpoints

- `GET /api/chat` - Health check
- `POST /api/chat` - Send message and get response
- `POST /api/chat/stream` - Send message with streaming response

## 🎨 Design System

- **Colors**: Dark theme with blue accents
- **Typography**: Geist font family
- **Components**: Modern UI components with hover effects
- **Responsive**: Mobile-first design approach

## 🔧 Development

### Frontend Development
```bash
npm run dev      # Start development server
npm run build    # Build for production
npm run lint     # Run ESLint
```

### Backend Development
```bash
cd backend
python main.py   # Start with auto-reload
```

## 📁 Project Structure

```
├── app/                    # Next.js app directory
│   ├── api/               # API routes
│   ├── client-layout.tsx  # Client-side layout
│   └── layout.tsx         # Root layout
├── backend/               # Python backend
│   ├── main.py           # FastAPI server
│   ├── crew.py           # CrewAI setup
│   └── requirements.txt  # Python dependencies
├── components/           # React components
├── chat-interface.tsx    # Main chat component
└── README.md
```

## 🚀 Deployment

### Frontend (Vercel)
1. Connect your repository to Vercel
2. Set environment variables in Vercel dashboard
3. Deploy automatically on push

### Backend (Railway/Heroku)
1. Deploy the `backend/` directory
2. Set environment variables
3. Update `BACKEND_URL` in frontend environment

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

If you encounter any issues:

1. Check that all environment variables are set correctly
2. Ensure both frontend and backend are running
3. Verify your API keys are valid
4. Check the console for error messages

For more help, please open an issue on GitHub.
