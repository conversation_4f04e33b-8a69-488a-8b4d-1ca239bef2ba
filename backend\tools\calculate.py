"""
Mathematical calculation tool for Make It Heavy system
"""

import ast
import operator
from typing import List
from .base_tool import BaseTool, ToolParameter

class CalculateTool(BaseTool):
    """Perform safe mathematical calculations and analysis"""
    
    def get_parameters(self) -> List[ToolParameter]:
        return [
            ToolParameter("expression", "string", "Mathematical expression to evaluate")
        ]
    
    async def execute(self, expression: str) -> str:
        """Execute mathematical calculation safely"""
        self.validate_parameters(expression=expression)
        
        try:
            # Safe evaluation of mathematical expressions
            ops = {
                ast.Add: operator.add,
                ast.Sub: operator.sub,
                ast.Mult: operator.mul,
                ast.Div: operator.truediv,
                ast.Pow: operator.pow,
                ast.USub: operator.neg,
                ast.Mod: operator.mod,
            }
            
            def eval_expr(node):
                if isinstance(node, ast.Num):  # number
                    return node.n
                elif isinstance(node, ast.Constant):  # Python 3.8+
                    return node.value
                elif isinstance(node, ast.BinOp):  # binary operation
                    return ops[type(node.op)](eval_expr(node.left), eval_expr(node.right))
                elif isinstance(node, ast.UnaryOp):  # unary operation
                    return ops[type(node.op)](eval_expr(node.operand))
                else:
                    raise TypeError(f"Unsupported operation: {type(node)}")
            
            # Parse and evaluate the expression
            parsed = ast.parse(expression, mode='eval')
            result = eval_expr(parsed.body)
            
            return f"**Calculation Result:**\n{expression} = {result}"
            
        except ZeroDivisionError:
            return f"**Calculation Error:** Division by zero in expression: {expression}"
        except (ValueError, TypeError, SyntaxError) as e:
            return f"**Calculation Error:** Invalid expression '{expression}': {str(e)}"
        except Exception as e:
            return f"**Calculation Error:** Unexpected error: {str(e)}"
