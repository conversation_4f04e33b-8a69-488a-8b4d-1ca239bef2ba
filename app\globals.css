@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 0 0% 0%; /* Pure black */
    --foreground: 0 0% 98%;
    --card: 0 0% 7%; /* neutral-900 equivalent */
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 7%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 10%; /* neutral-800 equivalent */
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 10%;
    --muted-foreground: 0 0% 64%; /* neutral-400 equivalent */
    --accent: 0 0% 10%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 20%; /* neutral-800 equivalent */
    --input: 0 0% 20%;
    --ring: 0 0% 83.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom gradient classes */
.bg-blue-gradient {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.bg-blue-gradient-hover {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  transition: all 0.2s ease;
}

.bg-blue-gradient-hover:hover {
  background: linear-gradient(135deg, #60a5fa 0%, #2563eb 100%);
  transform: translateY(-1px);
  box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.4);
}

/* Card hover effects */
.card-hover-blue {
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.card-hover-blue:hover {
  border-color: rgb(59 130 246 / 0.3);
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(59, 130, 246, 0.1);
}

.card-hover-blue::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.05), transparent);
  transition: left 0.5s ease;
}

.card-hover-blue:hover::before {
  left: 100%;
}

/* Glow effects */
.glow-blue {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.15);
}

.glow-blue-strong {
  box-shadow: 0 0 30px rgba(59, 130, 246, 0.3), 0 0 60px rgba(59, 130, 246, 0.1);
}

/* Reset default scrollbar styles */
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(64, 64, 64, 0.6) transparent;
}

/* Ultra-sleek webkit scrollbars */
*::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

*::-webkit-scrollbar-track {
  background: transparent;
  margin: 2px;
}

*::-webkit-scrollbar-thumb {
  background: rgba(64, 64, 64, 0.5);
  border-radius: 2px;
  border: none;
  transition: all 0.15s ease;
}

*::-webkit-scrollbar-thumb:hover {
  background: rgba(80, 80, 80, 0.7);
}

*::-webkit-scrollbar-corner {
  background: transparent;
}

/* Textarea specific scrollbars - even sleeker */
textarea {
  scrollbar-width: thin;
  scrollbar-color: rgba(48, 48, 48, 0.4) transparent;
}

textarea::-webkit-scrollbar {
  width: 3px;
  height: 3px;
}

textarea::-webkit-scrollbar-track {
  background: transparent;
  margin: 4px;
}

textarea::-webkit-scrollbar-thumb {
  background: rgba(48, 48, 48, 0.4);
  border-radius: 1.5px;
  transition: all 0.15s ease;
}

textarea::-webkit-scrollbar-thumb:hover {
  background: rgba(64, 64, 64, 0.6);
}

/* ScrollArea components - consistent with overall theme */
[data-radix-scroll-area-viewport] {
  scrollbar-width: thin;
  scrollbar-color: rgba(64, 64, 64, 0.4) transparent;
}

[data-radix-scroll-area-viewport]::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

[data-radix-scroll-area-viewport]::-webkit-scrollbar-track {
  background: transparent;
  margin: 2px;
}

[data-radix-scroll-area-viewport]::-webkit-scrollbar-thumb {
  background: rgba(64, 64, 64, 0.4);
  border-radius: 2px;
  transition: all 0.15s ease;
}

[data-radix-scroll-area-viewport]::-webkit-scrollbar-thumb:hover {
  background: rgba(80, 80, 80, 0.6);
}

/* Ensure scrollbars don't interfere with buttons */
.textarea-container {
  position: relative;
}

.textarea-with-button {
  padding-right: 48px !important; /* Extra space for button */
}

/* Mobile - completely hidden but functional */
@media (max-width: 768px) {
  * {
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  *::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
  }
}

/* Custom scrollbar for specific dark containers */
.dark-scrollbar::-webkit-scrollbar {
  width: 3px;
}

.dark-scrollbar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
}

.dark-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(32, 32, 32, 0.6);
  border-radius: 1.5px;
}

.dark-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(48, 48, 48, 0.8);
}
