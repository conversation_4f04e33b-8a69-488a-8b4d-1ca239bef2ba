from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
import httpx
import json
import os
import asyncio
from dotenv import load_dotenv
from make_it_heavy import make_it_heavy

# Load environment variables
load_dotenv()

app = FastAPI(title="AG3NT X Backend", version="1.0.0")

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class ChatMessage(BaseModel):
    message: str
    conversation_id: str = "default"
    mode: str = "single"  # "single" or "heavy"

class ChatResponse(BaseModel):
    response: str
    agent: str
    timestamp: str

# OpenRouter configuration
OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY")
OPENROUTER_MODEL = os.getenv("OPENROUTER_MODEL", "anthropic/claude-3.5-sonnet")
OPENROUTER_BASE_URL = "https://openrouter.ai/api/v1"

async def call_openrouter(message: str) -> str:
    """Call OpenRouter API directly"""
    
    if not OPENROUTER_API_KEY:
        return "Error: OpenRouter API key not configured. Please set OPENROUTER_API_KEY in your .env file."
    
    headers = {
        "Authorization": f"Bearer {OPENROUTER_API_KEY}",
        "Content-Type": "application/json",
        "HTTP-Referer": "http://localhost:3000",
        "X-Title": "AG3NT X",
    }
    
    payload = {
        "model": OPENROUTER_MODEL,
        "messages": [
            {
                "role": "system",
                "content": """You are AG3NT X, an advanced AI assistant with expertise across multiple domains. 
                You excel at understanding context, providing detailed explanations, and helping users solve problems. 
                You communicate in a friendly, professional manner and always strive to be helpful and accurate.
                
                You are part of a multi-agent system, so you can handle a wide variety of tasks including:
                - General conversation and questions
                - Technical problem solving
                - Research and analysis
                - Creative tasks
                - Programming help
                
                Always provide comprehensive, helpful responses."""
            },
            {
                "role": "user",
                "content": message
            }
        ],
        "temperature": 0.7,
        "max_tokens": 4000,
    }
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{OPENROUTER_BASE_URL}/chat/completions",
                headers=headers,
                json=payload,
                timeout=60.0
            )
            response.raise_for_status()
            
            result = response.json()
            
            if "choices" in result and len(result["choices"]) > 0:
                return result["choices"][0]["message"]["content"]
            else:
                return "I apologize, but I couldn't generate a response at this time."
                
    except httpx.HTTPStatusError as e:
        error_msg = f"HTTP error {e.response.status_code}"
        try:
            error_detail = e.response.json()
            if "error" in error_detail:
                error_msg += f": {error_detail['error'].get('message', 'Unknown error')}"
        except:
            pass
        return f"Error: {error_msg}"
        
    except Exception as e:
        return f"Error calling OpenRouter API: {str(e)}"

@app.get("/")
async def root():
    return {"message": "AG3NT X Backend is running", "model": OPENROUTER_MODEL}

@app.get("/health")
async def health_check():
    return {
        "status": "healthy", 
        "service": "AG3NT X Backend",
        "model": OPENROUTER_MODEL,
        "api_key_configured": bool(OPENROUTER_API_KEY)
    }

@app.post("/api/chat")
async def chat_endpoint(chat_message: ChatMessage):
    """
    Handle chat messages with single agent or heavy mode
    """
    try:
        if chat_message.mode == "heavy":
            # Use Make It Heavy multi-agent system
            result = await make_it_heavy(chat_message.message)

            return {
                "response": result["final_synthesis"],
                "agent": "Make It Heavy",
                "timestamp": result["timestamp"],
                "conversation_id": chat_message.conversation_id,
                "model": OPENROUTER_MODEL,
                "mode": "heavy",
                "agent_results": result["agent_results"],
                "generated_questions": result["generated_questions"]
            }
        else:
            # Single agent mode
            response = await call_openrouter(chat_message.message)

            return {
                "response": response,
                "agent": "AG3NT X",
                "timestamp": "2024-01-01T00:00:00Z",
                "conversation_id": chat_message.conversation_id,
                "model": OPENROUTER_MODEL,
                "mode": "single"
            }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing message: {str(e)}")

@app.post("/api/chat/heavy")
async def heavy_chat_stream(chat_message: ChatMessage):
    """
    Stream Make It Heavy processing with real-time progress updates
    """
    async def generate_heavy_response():
        try:
            progress_updates = []

            async def progress_callback(message: str):
                progress_data = {
                    "type": "progress",
                    "message": message,
                    "timestamp": "2024-01-01T00:00:00Z"
                }
                yield f"data: {json.dumps(progress_data)}\n\n"

            # Process with Make It Heavy
            result = await make_it_heavy(chat_message.message, progress_callback)

            # Send final result
            final_data = {
                "type": "complete",
                "response": result["final_synthesis"],
                "agent": "Make It Heavy",
                "timestamp": result["timestamp"],
                "conversation_id": chat_message.conversation_id,
                "model": OPENROUTER_MODEL,
                "mode": "heavy",
                "agent_results": result["agent_results"],
                "generated_questions": result["generated_questions"]
            }
            yield f"data: {json.dumps(final_data)}\n\n"

        except Exception as e:
            error_data = {
                "type": "error",
                "error": str(e),
                "timestamp": "2024-01-01T00:00:00Z"
            }
            yield f"data: {json.dumps(error_data)}\n\n"

    return StreamingResponse(
        generate_heavy_response(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
        }
    )

if __name__ == "__main__":
    import uvicorn
    print(f"🚀 Starting AG3NT X Backend with {OPENROUTER_MODEL}")
    print(f"🔑 API Key configured: {bool(OPENROUTER_API_KEY)}")
    uvicorn.run("simple_main:app", host="0.0.0.0", port=8000, reload=True)
