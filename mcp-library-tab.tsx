"use client"

import type React from "react"
import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>rollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Switch } from "@/components/ui/switch"
import {
  Database,
  Globe,
  FileText,
  Calendar,
  Mail,
  Code,
  Search,
  Download,
  CheckCircle,
  Star,
  Settings,
  Package,
  Plus,
} from "lucide-react"

interface McpServer {
  id: string
  name: string
  description: string
  author: string
  version: string
  category: string
  icon: React.ReactNode
  rating: number
  downloads: number
  tags: string[]
  status: "installed" | "available" | "updating"
  gradient: string
  lastUpdated: string
}

const mcpServers: McpServer[] = [
  {
    id: "1",
    name: "Database Connector",
    description:
      "Connect to various databases including PostgreSQL, MySQL, MongoDB, and SQLite. Execute queries and manage data seamlessly.",
    author: "Anthropic",
    version: "2.1.0",
    category: "Database",
    icon: <Database className="h-5 w-5" />,
    rating: 4.9,
    downloads: 25600,
    tags: ["SQL", "PostgreSQL", "MySQL", "MongoDB"],
    status: "installed",
    gradient: "from-blue-500 to-cyan-500",
    lastUpdated: "2 days ago",
  },
  {
    id: "2",
    name: "Web Search Tools",
    description:
      "Advanced web search capabilities with DuckDuckGo integration, web scraping, and content analysis for comprehensive research.",
    author: "AG3NT X Team",
    version: "1.0.0",
    category: "Web",
    icon: <Globe className="h-5 w-5" />,
    rating: 4.7,
    downloads: 890,
    tags: ["Search", "Web", "Research", "DuckDuckGo"],
    status: "installed",
    gradient: "from-green-500 to-emerald-500",
    lastUpdated: "1 day ago",
  },
  {
    id: "3",
    name: "Document Parser",
    description:
      "Parse and extract content from PDFs, Word documents, spreadsheets, and other file formats with AI-powered analysis.",
    author: "DocAI Labs",
    version: "3.0.1",
    category: "Documents",
    icon: <FileText className="h-5 w-5" />,
    rating: 4.8,
    downloads: 12400,
    tags: ["PDF", "Word", "Excel", "OCR"],
    status: "installed",
    gradient: "from-purple-500 to-pink-500",
    lastUpdated: "3 days ago",
  },
  {
    id: "4",
    name: "Math Calculator",
    description:
      "Safe mathematical calculations and analysis. Supports complex expressions, statistical functions, and data processing.",
    author: "AG3NT X Team",
    version: "1.0.0",
    category: "Productivity",
    icon: <Code className="h-5 w-5" />,
    rating: 4.6,
    downloads: 620,
    tags: ["Math", "Calculator", "Statistics", "Analysis"],
    status: "installed",
    gradient: "from-orange-500 to-red-500",
    lastUpdated: "1 day ago",
  },
]

const categories = ["All", "Database", "Web", "Documents", "Productivity"]

const getStatusColor = (status: string) => {
  switch (status) {
    case "installed":
      return "bg-green-500"
    case "updating":
      return "bg-yellow-500"
    case "available":
      return "bg-gray-500"
    default:
      return "bg-gray-500"
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case "installed":
      return "Installed"
    case "updating":
      return "Updating"
    case "available":
      return "Available"
    default:
      return "Unknown"
  }
}

export default function McpLibraryTab() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("All")
  const [serverStates, setServerStates] = useState<Record<string, boolean>>({
    "1": true, // Database Connector - enabled
    "2": true, // Document Parser - enabled
    "3": false, // Web Search Tools - disabled
    "4": true, // Math Calculator - enabled
  })

  const filteredServers = mcpServers.filter((server) => {
    const matchesSearch =
      server.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      server.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      server.tags.some((tag) => tag.toLowerCase().includes(searchQuery.toLowerCase()))

    const matchesCategory = selectedCategory === "All" || server.category === selectedCategory

    return matchesSearch && matchesCategory
  })

  const handleInstall = (serverId: string, serverName: string) => {
    console.log(`Installing MCP server: ${serverName} (ID: ${serverId})`)
    // Placeholder for installation logic
  }

  const handleUninstall = (serverId: string, serverName: string) => {
    console.log(`Uninstalling MCP server: ${serverName} (ID: ${serverId})`)
    // Placeholder for uninstallation logic
  }

  const handleServerSettings = (serverId: string, serverName: string) => {
    console.log(`Opening settings for MCP server: ${serverName} (ID: ${serverId})`)
    // Placeholder for settings logic
  }

  const handleServerToggle = (serverId: string, enabled: boolean) => {
    setServerStates(prev => ({
      ...prev,
      [serverId]: enabled
    }))
    console.log(`MCP server ${serverId} ${enabled ? 'enabled' : 'disabled'}`)
  }

  const handleAddNewServer = () => {
    console.log('Opening MCP server installation dialog')
    // Placeholder for adding new servers
  }

  const getStatusColorNew = (status: string) => {
    switch (status) {
      case "installed":
        return "bg-green-500"
      case "updating":
        return "bg-yellow-500"
      case "available":
        return "bg-neutral-500"
      default:
        return "bg-neutral-500"
    }
  }

  const getStatusTextNew = (status: string) => {
    switch (status) {
      case "installed":
        return "Installed"
      case "updating":
        return "Updating"
      case "available":
        return "Available"
      default:
        return "Unknown"
    }
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="px-6 py-4 border-b border-neutral-800">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-xl font-semibold text-white">MCP Library</h1>
            <p className="text-sm text-neutral-400">Installed Model Context Protocol servers</p>
          </div>
          <div className="flex items-center gap-3">
            <Badge variant="secondary" className="bg-gray-800 text-gray-300">
              {filteredServers.length} installed
            </Badge>
            <Button
              onClick={handleAddNewServer}
              className="bg-blue-gradient-hover text-white shadow-lg"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Server
            </Button>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="flex gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search MCP servers..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 bg-neutral-900 border-neutral-800 text-white placeholder:text-neutral-500 focus:border-blue-500/40"
            />
          </div>

          <div className="flex gap-2">
            {categories.map((category) => (
              <Button
                key={category}
                variant="ghost"
                size="sm"
                onClick={() => setSelectedCategory(category)}
                className={`${
                  selectedCategory === category
                    ? "border-blue-500/20 text-blue-400 hover:bg-blue-500/10 hover:text-blue-300"
                    : "text-gray-400 hover:text-white hover:bg-gray-800/50"
                }`}
              >
                {category}
              </Button>
            ))}
          </div>
        </div>
      </div>

      {/* MCP Servers Grid */}
      <ScrollArea className="flex-1 p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredServers.map((server) => (
            <div
              key={server.id}
              className="group relative bg-neutral-900 border border-neutral-800 rounded-xl card-hover-blue p-5 transition-all duration-200"
            >
              {/* Settings Gear Icon */}
              {server.status === "installed" && (
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    handleServerSettings(server.id, server.name)
                  }}
                  className="absolute top-4 right-4 w-7 h-7 rounded-lg bg-neutral-900/70 hover:bg-neutral-800/70 border border-neutral-700/50 hover:border-neutral-600 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-200 z-10"
                  title={`Settings for ${server.name}`}
                >
                  <Settings className="h-3.5 w-3.5 text-neutral-400 hover:text-neutral-300" />
                </button>
              )}

              {/* Server Header */}
              <div className="flex items-start gap-4 mb-4">
                <div
                  className={`w-12 h-12 rounded-xl bg-gradient-to-br ${server.gradient} flex items-center justify-center text-white flex-shrink-0`}
                >
                  {server.icon}
                </div>

                <div className="flex-1 min-w-0 pr-8">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="font-semibold text-white truncate">{server.name}</h3>
                    <div className="flex items-center gap-1">
                      <div className={`w-2 h-2 rounded-full ${getStatusColorNew(server.status)}`} />
                      <span className="text-xs text-neutral-400">{getStatusTextNew(server.status)}</span>
                    </div>
                  </div>

                  <div className="flex items-center gap-2 mb-2">
                    <p className="text-sm text-neutral-400">{server.category}</p>
                    <span className="text-xs text-gray-500">•</span>
                    <p className="text-xs text-gray-500">v{server.version}</p>
                    <span className="text-xs text-gray-500">•</span>
                    <p className="text-xs text-gray-500">by {server.author}</p>
                  </div>

                  <div className="flex items-center gap-4 text-xs text-gray-500">
                    <div className="flex items-center gap-1">
                      <Star className="h-3 w-3 fill-yellow-500 text-yellow-500" />
                      <span>{server.rating}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Download className="h-3 w-3" />
                      <span>{server.downloads.toLocaleString()}</span>
                    </div>
                    <span>Updated {server.lastUpdated}</span>
                  </div>
                </div>
              </div>

              {/* Description */}
              <p className="text-sm text-neutral-400 mb-4 leading-relaxed">{server.description}</p>

              {/* Tags */}
              <div className="flex flex-wrap gap-2 mb-4">
                {server.tags.map((tag) => (
                  <Badge
                    key={tag}
                    variant="secondary"
                    className="bg-neutral-800 text-neutral-400 border-neutral-700 text-xs px-2 py-1 hover:bg-gray-700"
                  >
                    {tag}
                  </Badge>
                ))}
              </div>

              {/* Toggle Control */}
              <div className="flex items-center justify-between p-3 bg-neutral-800 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className={`w-2 h-2 rounded-full ${serverStates[server.id] ? 'bg-green-500' : 'bg-neutral-500'}`} />
                  <span className="text-sm font-medium text-white">
                    {serverStates[server.id] ? 'Enabled' : 'Disabled'}
                  </span>
                </div>
                <Switch
                  checked={serverStates[server.id]}
                  onCheckedChange={(checked) => handleServerToggle(server.id, checked)}
                  className="data-[state=checked]:bg-blue-500"
                />
              </div>

              {/* Hover Effect */}
              <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
            </div>
          ))}
        </div>

        {filteredServers.length === 0 && (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <Package className="h-12 w-12 text-gray-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-400 mb-2">No MCP servers found</h3>
              <p className="text-sm text-gray-500">Try adjusting your search or filter criteria</p>
            </div>
          </div>
        )}
      </ScrollArea>
    </div>
  )
}
