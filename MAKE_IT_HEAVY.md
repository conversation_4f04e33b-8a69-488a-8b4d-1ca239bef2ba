# 🧠 Make It Heavy - Grok Heavy Emulation System

A Python framework to emulate Grok heavy functionality using a powerful multi-agent system. Built on OpenRouter's API, Make It Heavy delivers comprehensive, multi-perspective analysis through intelligent agent orchestration.

## 🌟 Features

- 🧠 **Grok Heavy Emulation**: Multi-agent system that delivers deep, comprehensive analysis like Grok heavy mode
- 🔀 **Parallel Intelligence**: Deploy 4 specialized agents simultaneously for maximum insight coverage  
- 🎯 **Dynamic Question Generation**: AI creates custom research questions tailored to each query
- ⚡ **Real-time Orchestration**: Live visual feedback during multi-agent execution
- 🛠️ **Hot-Swappable Tools**: Automatically discovers and loads tools from the tools/ directory
- 🔄 **Intelligent Synthesis**: Combines multiple agent perspectives into unified, comprehensive answers
- 🎮 **Single Agent Mode**: Run individual agents for simpler tasks with full tool access

## 🏗️ Architecture

```
User Input → Question Generation → 4 Parallel Agents → Synthesis → Final Answer
    ↓              ↓                    ↓               ↓           ↓
  Query      AI Generated         Research         Combine      Grok Heavy
           Questions           Analysis         Perspectives    Response
                              Alternatives
                              Verification
```

### Core Components

1. **Agent System** (`make_it_heavy.py`)
   - Self-contained: Complete agent implementation with tool access
   - Agentic Loop: Continues working until task completion  
   - Tool Integration: Automatic tool discovery and execution
   - Configurable: Uses OpenRouter for all AI operations

2. **Orchestrator** (`HeavyOrchestrator`)
   - Dynamic Question Generation: AI creates specialized questions
   - Parallel Execution: Runs multiple agents simultaneously
   - Response Synthesis: AI combines all agent outputs
   - Error Handling: Graceful fallbacks and error recovery

3. **Tool System** (`tools/`)
   - Auto-Discovery: Automatically loads all tools from directory
   - Hot-Swappable: Add new tools by dropping files in tools/
   - Standardized Interface: All tools inherit from BaseTool

## 🛠️ Available Tools

| Tool | Purpose | Parameters |
|------|---------|------------|
| `search_web` | Web search with DuckDuckGo | `query`, `max_results` |
| `calculate` | Safe mathematical calculations | `expression` |
| `mark_task_complete` | Signal task completion | `task_summary`, `completion_message` |

## 🎯 Usage

### Single Agent Mode
Quick responses with full tool access:

```bash
# Frontend: Select "Single Agent" mode
# Backend: Processes with single intelligent agent
```

**What it does:**
- Loads a single agent with all available tools
- Processes your query step-by-step  
- Uses tools like web search, calculator, file operations
- Returns comprehensive response when task is complete

### Make It Heavy Mode (Multi-Agent Orchestration)
Emulate Grok heavy's deep analysis with 4 parallel intelligent agents:

```bash
# Frontend: Select "Make It Heavy" mode  
# Backend: Runs full multi-agent orchestration
```

**How Make It Heavy works:**

1. 🎯 **AI Question Generation**: Creates 4 specialized research questions from your query
2. 🔀 **Parallel Intelligence**: Runs 4 agents simultaneously with different analytical perspectives
3. ⚡ **Live Progress**: Shows real-time agent status with visual progress bars
4. 🔄 **Intelligent Synthesis**: Combines all perspectives into one comprehensive Grok heavy-style answer

**Example Flow:**

```
User Query: "Who is Pietro Schirano?"

AI Generated Questions:
- Agent 1 (Researcher): "Research Pietro Schirano's professional background and career history"
- Agent 2 (Analyst): "Analyze Pietro Schirano's achievements and contributions to technology"  
- Agent 3 (Contextualizer): "Find alternative perspectives on Pietro Schirano's work and impact"
- Agent 4 (Verifier): "Verify and cross-check information about Pietro Schirano's current role"

Result: Grok heavy-style comprehensive analysis combining all agent perspectives
```

## 🚀 API Endpoints

### Single Agent Chat
```http
POST /api/chat
Content-Type: application/json

{
  "message": "Your question here",
  "mode": "single",
  "conversation_id": "default"
}
```

### Make It Heavy Chat  
```http
POST /api/chat
Content-Type: application/json

{
  "message": "Your question here", 
  "mode": "heavy",
  "conversation_id": "default"
}
```

### Streaming Heavy Mode
```http
POST /api/chat/heavy
Content-Type: application/json

{
  "message": "Your question here",
  "conversation_id": "default"
}
```

Returns Server-Sent Events with real-time progress updates.

## 🔧 Configuration

### Environment Variables
```env
OPENROUTER_API_KEY=your_openrouter_api_key_here
OPENROUTER_MODEL=anthropic/claude-3.5-sonnet
```

### Supported Models
- `anthropic/claude-3.5-sonnet` (default, best performance)
- `openai/gpt-4o` (OpenAI's latest)
- `google/gemini-pro` (Google's model)
- `meta-llama/llama-3.1-405b-instruct` (Open source)

## 🛠️ Adding New Tools

1. Create a new file in `backend/tools/`
2. Inherit from `BaseTool`
3. Implement required methods

```python
from .base_tool import BaseTool, ToolParameter

class MyCustomTool(BaseTool):
    """Description of what this tool does"""
    
    def get_parameters(self):
        return [
            ToolParameter("param1", "string", "Description of param1"),
            ToolParameter("param2", "integer", "Description of param2", False, 10)
        ]
    
    async def execute(self, param1: str, param2: int = 10) -> str:
        # Your tool logic here
        return f"Tool executed with {param1} and {param2}"
```

The tool will be automatically discovered and loaded!

## 🎮 Frontend Features

- **Mode Selector**: Toggle between Single Agent and Make It Heavy
- **Real-time Progress**: Visual feedback during heavy processing
- **Agent Indicators**: Different avatars for single vs heavy mode
- **Streaming Updates**: Live progress messages during multi-agent execution

## 🚀 Getting Started

1. **Start the backend:**
   ```bash
   cd backend
   python simple_main.py
   ```

2. **Start the frontend:**
   ```bash
   npm run dev
   ```

3. **Open browser:** `http://localhost:3000`

4. **Try both modes:**
   - Single Agent: Quick, focused responses
   - Make It Heavy: Comprehensive, multi-perspective analysis

## 🎯 Perfect For

- **Research Tasks**: Multi-perspective analysis of complex topics
- **Decision Making**: Comprehensive evaluation of options
- **Problem Solving**: Deep analysis with verification
- **Learning**: Understanding topics from multiple angles
- **Professional Analysis**: Thorough investigation and reporting

Experience the power of multi-agent AI orchestration with Make It Heavy! 🧠⚡
