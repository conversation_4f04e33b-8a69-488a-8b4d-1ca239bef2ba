import { NextRequest } from 'next/server'

const BACKEND_URL = process.env.BACKEND_URL || 'http://localhost:8000'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { message, conversation_id = 'default' } = body

    if (!message) {
      return new Response('Message is required', { status: 400 })
    }

    // Forward the request to the Make It Heavy backend endpoint
    const response = await fetch(`${BACKEND_URL}/api/chat/heavy`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message,
        conversation_id,
      }),
    })

    if (!response.ok) {
      return new Response(`Backend error: ${response.status}`, { status: response.status })
    }

    // Create a readable stream to forward the backend response
    const stream = new ReadableStream({
      start(controller) {
        const reader = response.body?.getReader()
        
        function pump(): Promise<void> {
          return reader!.read().then(({ done, value }) => {
            if (done) {
              controller.close()
              return
            }
            
            controller.enqueue(value)
            return pump()
          })
        }
        
        return pump()
      }
    })

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    })

  } catch (error) {
    console.error('Heavy chat API error:', error)
    return new Response('Internal server error', { status: 500 })
  }
}
