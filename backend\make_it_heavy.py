"""
Make It Heavy - Grok Heavy Emulation System
A powerful multi-agent orchestration system for comprehensive analysis
"""

import asyncio
import json
import os
import importlib
import inspect
from typing import List, Dict, Any
from dataclasses import dataclass
from datetime import datetime
import httpx
from dotenv import load_dotenv
from tools.base_tool import BaseTool

load_dotenv()

@dataclass
class AgentTask:
    id: str
    question: str
    status: str = "pending"  # pending, running, completed, failed
    result: str = ""
    agent_type: str = ""
    start_time: datetime = None
    end_time: datetime = None

class OpenRouterClient:
    def __init__(self):
        self.api_key = os.getenv("OPENROUTER_API_KEY")
        self.model = os.getenv("OPENROUTER_MODEL", "anthropic/claude-3.5-sonnet")
        self.base_url = "https://openrouter.ai/api/v1"
        
    async def chat_completion(self, messages: List[Dict], temperature: float = 0.7) -> str:
        """Make a chat completion request to OpenRouter"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "http://localhost:3000",
            "X-Title": "Make It Heavy",
        }
        
        payload = {
            "model": self.model,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": 4000,
        }
        
        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=payload
            )
            response.raise_for_status()
            result = response.json()
            return result["choices"][0]["message"]["content"]

class QuestionGenerator:
    def __init__(self, client: OpenRouterClient):
        self.client = client
        
    async def generate_questions(self, user_query: str) -> List[str]:
        """Generate 4 specialized research questions for parallel agent execution"""
        system_prompt = """You are a question generation specialist for a multi-agent research system.
        
        Given a user query, generate exactly 4 specialized research questions that will enable comprehensive analysis.
        Each question should focus on a different analytical perspective:
        
        1. Research & Facts: Gather core information and factual data
        2. Analysis & Insights: Deep analytical perspective and implications  
        3. Alternatives & Context: Alternative viewpoints and broader context
        4. Verification & Validation: Cross-checking and verification of information
        
        Return ONLY a JSON array of 4 strings, nothing else.
        Example: ["Question 1", "Question 2", "Question 3", "Question 4"]"""
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": f"Generate 4 specialized research questions for: {user_query}"}
        ]
        
        response = await self.client.chat_completion(messages, temperature=0.3)
        
        try:
            # Extract JSON from response
            import re
            json_match = re.search(r'\[.*\]', response, re.DOTALL)
            if json_match:
                questions = json.loads(json_match.group())
                return questions[:4]  # Ensure exactly 4 questions
        except:
            pass
            
        # Fallback questions if parsing fails
        return [
            f"Research the core facts and background information about: {user_query}",
            f"Analyze the implications and deeper meaning of: {user_query}",
            f"Explore alternative perspectives and broader context for: {user_query}",
            f"Verify and cross-check key information about: {user_query}"
        ]

class SpecializedAgent:
    def __init__(self, client: OpenRouterClient, agent_type: str):
        self.client = client
        self.agent_type = agent_type
        self.tools = self._load_tools()
        
    def _load_tools(self) -> Dict[str, BaseTool]:
        """Auto-discover and load tools from the tools directory"""
        tools = {}
        tools_dir = os.path.join(os.path.dirname(__file__), 'tools')

        # Get all Python files in tools directory
        for filename in os.listdir(tools_dir):
            if filename.endswith('.py') and not filename.startswith('__') and filename != 'base_tool.py':
                module_name = filename[:-3]  # Remove .py extension

                try:
                    # Import the module
                    module = importlib.import_module(f'tools.{module_name}')

                    # Find tool classes in the module
                    for name, obj in inspect.getmembers(module):
                        if (inspect.isclass(obj) and
                            issubclass(obj, BaseTool) and
                            obj != BaseTool):
                            tool_instance = obj()
                            tools[tool_instance.name] = tool_instance

                except Exception as e:
                    print(f"Warning: Could not load tool from {filename}: {e}")

        return tools
        
    async def _execute_tool(self, tool_name: str, **kwargs) -> str:
        """Execute a tool by name with given parameters"""
        if tool_name not in self.tools:
            return f"Tool '{tool_name}' not available. Available tools: {list(self.tools.keys())}"

        try:
            tool = self.tools[tool_name]
            result = await tool.execute(**kwargs)
            return result
        except Exception as e:
            return f"Error executing tool '{tool_name}': {str(e)}"

    async def execute_task(self, task: AgentTask) -> str:
        """Execute a specialized task with tool access"""
        agent_personas = {
            "researcher": "You are a meticulous researcher focused on gathering comprehensive factual information.",
            "analyst": "You are a deep analytical thinker who provides insights and implications.",
            "contextualizer": "You are a broad-thinking agent who explores alternatives and context.",
            "verifier": "You are a verification specialist who cross-checks and validates information."
        }
        
        tools_description = "\n".join([
            f"- {name}: {tool.description}"
            for name, tool in self.tools.items()
        ])

        system_prompt = f"""{agent_personas.get(self.agent_type, "You are a specialized AI agent.")}

        You have access to these tools:
        {tools_description}

        Your task: {task.question}

        Work systematically to complete this task. Use tools when needed and provide a comprehensive response.
        Be thorough but concise. Focus on your specialized perspective.

        When you need to use a tool, clearly state which tool you're using and why.
        Provide detailed analysis and insights based on the information you gather."""
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": task.question}
        ]
        
        response = await self.client.chat_completion(messages, temperature=0.7)
        return response

class HeavyOrchestrator:
    def __init__(self):
        self.client = OpenRouterClient()
        self.question_generator = QuestionGenerator(self.client)
        self.agents = {
            "researcher": SpecializedAgent(self.client, "researcher"),
            "analyst": SpecializedAgent(self.client, "analyst"), 
            "contextualizer": SpecializedAgent(self.client, "contextualizer"),
            "verifier": SpecializedAgent(self.client, "verifier")
        }
        
    async def process_heavy_query(self, user_query: str, progress_callback=None) -> Dict[str, Any]:
        """Process a query using the full Make It Heavy multi-agent system"""
        
        # Step 1: Generate specialized questions
        if progress_callback:
            await progress_callback("Generating specialized research questions...")
            
        questions = await self.question_generator.generate_questions(user_query)
        
        # Step 2: Create agent tasks
        tasks = []
        agent_types = ["researcher", "analyst", "contextualizer", "verifier"]
        
        for i, (question, agent_type) in enumerate(zip(questions, agent_types)):
            task = AgentTask(
                id=f"task_{i+1}",
                question=question,
                agent_type=agent_type,
                start_time=datetime.now()
            )
            tasks.append(task)
            
        # Step 3: Execute tasks in parallel
        if progress_callback:
            await progress_callback("Executing parallel agent analysis...")
            
        async def execute_single_task(task: AgentTask) -> AgentTask:
            try:
                task.status = "running"
                if progress_callback:
                    await progress_callback(f"Agent {task.agent_type} working on: {task.question[:50]}...")
                    
                agent = self.agents[task.agent_type]
                task.result = await agent.execute_task(task)
                task.status = "completed"
                task.end_time = datetime.now()
                
                if progress_callback:
                    await progress_callback(f"Agent {task.agent_type} completed analysis")
                    
            except Exception as e:
                task.status = "failed"
                task.result = f"Error: {str(e)}"
                task.end_time = datetime.now()
                
            return task
            
        # Run all tasks in parallel
        completed_tasks = await asyncio.gather(*[execute_single_task(task) for task in tasks])
        
        # Step 4: Synthesize results
        if progress_callback:
            await progress_callback("Synthesizing comprehensive analysis...")
            
        synthesis = await self._synthesize_results(user_query, completed_tasks)
        
        return {
            "original_query": user_query,
            "generated_questions": questions,
            "agent_results": [
                {
                    "agent_type": task.agent_type,
                    "question": task.question,
                    "result": task.result,
                    "status": task.status,
                    "execution_time": (task.end_time - task.start_time).total_seconds() if task.end_time else None
                }
                for task in completed_tasks
            ],
            "final_synthesis": synthesis,
            "timestamp": datetime.now().isoformat()
        }
        
    async def _synthesize_results(self, original_query: str, tasks: List[AgentTask]) -> str:
        """Synthesize all agent results into a comprehensive final answer"""
        
        results_summary = "\n\n".join([
            f"**{task.agent_type.title()} Analysis:**\n{task.result}"
            for task in tasks if task.status == "completed"
        ])
        
        synthesis_prompt = f"""You are a synthesis specialist creating a comprehensive Grok-heavy style analysis.

Original Query: {original_query}

Agent Results:
{results_summary}

Create a comprehensive, well-structured final answer that:
1. Synthesizes insights from all agent perspectives
2. Provides a complete picture addressing the original query
3. Highlights key findings and implications
4. Maintains the depth and thoroughness of Grok heavy mode
5. Is well-organized and easy to follow

Deliver a comprehensive response that combines all perspectives into unified insights."""

        messages = [
            {"role": "system", "content": "You are an expert synthesis agent creating comprehensive Grok-heavy style responses."},
            {"role": "user", "content": synthesis_prompt}
        ]
        
        return await self.client.chat_completion(messages, temperature=0.6)

# Global orchestrator instance
heavy_orchestrator = HeavyOrchestrator()

async def make_it_heavy(query: str, progress_callback=None) -> Dict[str, Any]:
    """Main entry point for Make It Heavy processing"""
    return await heavy_orchestrator.process_heavy_query(query, progress_callback)
