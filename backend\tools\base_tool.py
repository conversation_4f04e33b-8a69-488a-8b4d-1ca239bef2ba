"""
Base tool class for Make It Heavy system
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional
from dataclasses import dataclass

@dataclass
class ToolParameter:
    name: str
    type: str
    description: str
    required: bool = True
    default: Any = None

class BaseTool(ABC):
    """Base class for all tools in the Make It Heavy system"""
    
    def __init__(self):
        self.name = self.__class__.__name__.lower().replace('tool', '')
        self.description = self.__doc__ or "No description available"
        self.parameters = self.get_parameters()
    
    @abstractmethod
    def get_parameters(self) -> List[ToolParameter]:
        """Return list of parameters this tool accepts"""
        pass
    
    @abstractmethod
    async def execute(self, **kwargs) -> str:
        """Execute the tool with given parameters"""
        pass
    
    def get_schema(self) -> Dict[str, Any]:
        """Get tool schema for AI model"""
        return {
            "name": self.name,
            "description": self.description,
            "parameters": {
                "type": "object",
                "properties": {
                    param.name: {
                        "type": param.type,
                        "description": param.description
                    }
                    for param in self.parameters
                },
                "required": [param.name for param in self.parameters if param.required]
            }
        }
    
    def validate_parameters(self, **kwargs) -> bool:
        """Validate that required parameters are provided"""
        required_params = {param.name for param in self.parameters if param.required}
        provided_params = set(kwargs.keys())
        
        missing_params = required_params - provided_params
        if missing_params:
            raise ValueError(f"Missing required parameters: {missing_params}")
        
        return True
