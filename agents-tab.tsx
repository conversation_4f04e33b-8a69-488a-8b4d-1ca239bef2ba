"use client"

import type React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>rollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import { <PERSON>ap, Brain, Code, Palette, MessageSquare, Star, Users, Settings } from "lucide-react"

interface Agent {
  id: string
  name: string
  description: string
  avatar: React.ReactNode
  specialty: string
  rating: number
  users: number
  tags: string[]
  status: "online" | "offline" | "busy"
  gradient: string
}

const agents: Agent[] = [
  {
    id: "1",
    name: "CodeMaster AI",
    description:
      "Expert in programming, debugging, and software architecture. Specializes in multiple languages and frameworks.",
    avatar: <Code className="h-5 w-5" />,
    specialty: "Programming",
    rating: 4.9,
    users: 12500,
    tags: ["JavaScript", "Python", "React", "Node.js"],
    status: "online",
    gradient: "from-blue-500 to-cyan-500",
  },
  {
    id: "2",
    name: "Creative Studio",
    description:
      "AI assistant for creative projects, design thinking, and artistic endeavors. Perfect for brainstorming and ideation.",
    avatar: <Palette className="h-5 w-5" />,
    specialty: "Design & Creative",
    rating: 4.8,
    users: 8900,
    tags: ["Design", "Art", "Branding", "UI/UX"],
    status: "online",
    gradient: "from-purple-500 to-pink-500",
  },
  {
    id: "3",
    name: "DataWiz Pro",
    description:
      "Advanced data analysis, machine learning, and statistical modeling. Your go-to for complex data problems.",
    avatar: <Brain className="h-5 w-5" />,
    specialty: "Data Science",
    rating: 4.9,
    users: 6700,
    tags: ["ML", "Analytics", "Statistics", "Python"],
    status: "busy",
    gradient: "from-green-500 to-emerald-500",
  },
  {
    id: "4",
    name: "ChatBot Builder",
    description: "Specialized in conversational AI, chatbot development, and natural language processing solutions.",
    avatar: <MessageSquare className="h-5 w-5" />,
    specialty: "Conversational AI",
    rating: 4.7,
    users: 4200,
    tags: ["NLP", "Chatbots", "AI", "Automation"],
    status: "online",
    gradient: "from-orange-500 to-red-500",
  },
  {
    id: "5",
    name: "Speed Demon",
    description:
      "Lightning-fast responses for quick tasks, rapid prototyping, and instant solutions to common problems.",
    avatar: <Zap className="h-5 w-5" />,
    specialty: "Quick Tasks",
    rating: 4.6,
    users: 15600,
    tags: ["Fast", "Efficient", "Quick", "Productivity"],
    status: "online",
    gradient: "from-yellow-500 to-orange-500",
  },
  {
    id: "6",
    name: "Team Coordinator",
    description:
      "Perfect for team collaboration, project management, and coordinating group efforts across different domains.",
    avatar: <Users className="h-5 w-5" />,
    specialty: "Team Management",
    rating: 4.8,
    users: 3400,
    tags: ["Management", "Teams", "Coordination", "Planning"],
    status: "offline",
    gradient: "from-indigo-500 to-purple-500",
  },
]

const getStatusColor = (status: string) => {
  switch (status) {
    case "online":
      return "bg-green-500"
    case "busy":
      return "bg-yellow-500"
    case "offline":
      return "bg-neutral-500"
    default:
      return "bg-neutral-500"
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case "online":
      return "Online"
    case "busy":
      return "Busy"
    case "offline":
      return "Offline"
    default:
      return "Unknown"
  }
}

export default function AgentsTab() {
  const handleAgentSettings = (agentId: string, agentName: string) => {
    // Placeholder for future settings functionality
    console.log(`Opening settings for ${agentName} (ID: ${agentId})`)
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="h-16 px-6 border-b border-neutral-800 flex items-center justify-between">
        <div>
          <h1 className="text-xl font-semibold text-white">AI Agents</h1>
          <p className="text-sm text-neutral-400">Choose your specialized assistant</p>
        </div>
        <Badge variant="secondary" className="bg-neutral-500/10 text-neutral-400 border-neutral-500/20">
          {agents.length} Available
        </Badge>
      </div>

      {/* Agents Grid */}
      <ScrollArea className="flex-1 p-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          {agents.map((agent) => (
            <div
              key={agent.id}
              className="group relative bg-neutral-900 border border-neutral-800 rounded-xl card-hover-blue p-6"
            >
              {/* Settings Gear Icon */}
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  handleAgentSettings(agent.id, agent.name)
                }}
                className="absolute top-4 right-4 w-7 h-7 rounded-lg bg-neutral-800/50 hover:bg-neutral-700/70 border border-neutral-700/50 hover:border-neutral-600 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-200 z-10"
                title={`Settings for ${agent.name}`}
              >
                <Settings className="h-3.5 w-3.5 text-gray-400 hover:text-gray-300" />
              </button>

              {/* Agent Header */}
              <div className="flex items-start gap-4 mb-4">
                <div
                  className={`w-12 h-12 rounded-xl bg-gradient-to-br ${agent.gradient} flex items-center justify-center text-white flex-shrink-0`}
                >
                  {agent.avatar}
                </div>

                <div className="flex-1 min-w-0 pr-8">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="font-semibold text-white truncate">{agent.name}</h3>
                    <div className="flex items-center gap-1">
                      <div className={`w-2 h-2 rounded-full ${getStatusColor(agent.status)}`} />
                      <span className="text-xs text-neutral-400">{getStatusText(agent.status)}</span>
                    </div>
                  </div>

                  <p className="text-sm text-neutral-400 mb-2">{agent.specialty}</p>

                  <div className="flex items-center gap-4 text-xs text-gray-500">
                    <div className="flex items-center gap-1">
                      <Star className="h-3 w-3 fill-yellow-500 text-yellow-500" />
                      <span>{agent.rating}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Users className="h-3 w-3" />
                      <span>{agent.users.toLocaleString()}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Description */}
              <p className="text-sm text-neutral-400 mb-4 leading-relaxed">{agent.description}</p>

              {/* Tags */}
              <div className="flex flex-wrap gap-2 mb-4">
                {agent.tags.map((tag) => (
                  <Badge
                    key={tag}
                    variant="secondary"
                    className="bg-neutral-800 text-neutral-400 border-neutral-700 text-xs px-2 py-1 hover:bg-gray-700"
                  >
                    {tag}
                  </Badge>
                ))}
              </div>

              {/* Action Button */}
              <Button
                className={`w-full bg-gradient-to-r ${agent.gradient} hover:opacity-90 text-white border-0 transition-all duration-200 group-hover:shadow-lg`}
                disabled={agent.status === "offline"}
              >
                {agent.status === "offline" ? "Unavailable" : "Start Chat"}
              </Button>

              {/* Hover Effect */}
              <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
            </div>
          ))}
        </div>
      </ScrollArea>
    </div>
  )
}
